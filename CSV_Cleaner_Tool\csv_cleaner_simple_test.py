#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تنظيف CSV مبسطة للاختبار
"""

import os
import csv
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from io import StringIO

class SimpleCSVCleaner:
    def __init__(self, root):
        self.root = root
        self.root.title("🧹 CSV Cleaner - Simple Test")
        self.root.geometry("600x400")
        
        # النسق الثابت (19 عمود)
        self.standard_headers = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        
        self.setup_ui()

    def setup_ui(self):
        # العنوان
        title_label = tk.Label(self.root, text="🧹 CSV Cleaner - Simple Test", 
                              font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)
        
        # مجلد الإدخال
        ttk.Label(self.root, text="📂 مجلد الإدخال:").pack(anchor='w', padx=10)
        input_frame = ttk.Frame(self.root)
        input_frame.pack(fill='x', padx=10, pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(self.root, text="💾 مجلد الإخراج:").pack(anchor='w', padx=10, pady=(10, 0))
        output_frame = ttk.Frame(self.root)
        output_frame.pack(fill='x', padx=10, pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')
        
        # زر المعالجة
        ttk.Button(self.root, text="🚀 بدء التنظيف", 
                  command=self.start_cleaning).pack(pady=20)
        
        # الحالة
        ttk.Label(self.root, text="ℹ️ الحالة:").pack(anchor='w', padx=10)
        ttk.Label(self.root, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w', padx=10)
        
        # منطقة النتائج
        self.result_text = tk.Text(self.root, height=10, wrap=tk.WORD)
        self.result_text.pack(fill='both', expand=True, padx=10, pady=10)

    def browse_input(self):
        folder = filedialog.askdirectory(title="اختر مجلد الإدخال")
        if folder:
            self.input_folder.set(folder)

    def browse_output(self):
        folder = filedialog.askdirectory(title="اختر مجلد الإخراج")
        if folder:
            self.output_folder.set(folder)

    def log(self, message):
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()

    def fix_multiple_commas_in_line(self, line):
        """إصلاح الفواصل المتعددة"""
        # الجولة الأولى
        line = line.replace(',,,,,,', ',')
        line = line.replace(',,,,,', ',')
        line = line.replace(',,,,', ',')
        line = line.replace(',,,', ',')
        line = line.replace(',,', ',')
        
        # الجولة الثانية
        line = line.replace(',,,,,,', ',')
        line = line.replace(',,,,,', ',')
        line = line.replace(',,,,', ',')
        line = line.replace(',,,', ',')
        line = line.replace(',,', ',')
        
        return line

    def start_cleaning(self):
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.result_text.delete(1.0, tk.END)
        self.log("🚀 بدء عملية التنظيف...")
        
        try:
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            self.log(f"📊 وجد {len(csv_files)} ملف CSV")
            
            for i, csv_file in enumerate(csv_files):
                self.log(f"\n📄 معالجة الملف {i+1}: {csv_file.name}")
                self.status.set(f"معالجة {csv_file.name}")
                
                # قراءة الملف
                with open(csv_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                self.log(f"📊 حجم المحتوى الأصلي: {len(content)} حرف")
                
                # إصلاح الفواصل المتعددة
                lines = content.split('\n')
                fixed_lines = []
                
                for line in lines:
                    if line.strip():
                        fixed_line = self.fix_multiple_commas_in_line(line)
                        fixed_lines.append(fixed_line)
                    else:
                        fixed_lines.append(line)
                
                content = '\n'.join(fixed_lines)
                self.log(f"📊 حجم المحتوى بعد الإصلاح: {len(content)} حرف")
                
                # تحويل إلى CSV reader
                csv_reader = csv.reader(StringIO(content), delimiter=',')
                data_rows = list(csv_reader)
                
                self.log(f"📊 عدد الأسطر: {len(data_rows)}")
                if data_rows:
                    self.log(f"📊 عدد الأعمدة: {len(data_rows[0])}")
                
                # تطبيق النسق الثابت
                if len(data_rows) > 0:
                    standardized_data = [self.standard_headers]
                    
                    for row in data_rows[1:]:  # تخطي الرؤوس
                        new_row = [''] * 19
                        for j, cell in enumerate(row):
                            if j < 19:
                                new_row[j] = str(cell).replace('"', '').strip()
                        
                        if any(cell.strip() for cell in new_row):
                            standardized_data.append(new_row)
                    
                    self.log(f"📊 البيانات النهائية: {len(standardized_data)} سطر")
                    
                    # حفظ الملف
                    output_file = Path(self.output_folder.get()) / f"{csv_file.stem}_cleaned.csv"
                    
                    with open(output_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f, delimiter=',', quoting=csv.QUOTE_MINIMAL)
                        writer.writerows(standardized_data)
                    
                    self.log(f"✅ تم حفظ: {output_file.name}")
                    self.log(f"📊 عدد السجلات: {len(standardized_data)-1}")
                
                self.root.update_idletasks()
            
            self.status.set("✅ تم الانتهاء!")
            self.log(f"\n🎉 تم الانتهاء من معالجة {len(csv_files)} ملف!")
            messagebox.showinfo("مكتمل", f"تم تنظيف {len(csv_files)} ملف بنجاح!")
            
        except Exception as e:
            error_msg = f"حدث خطأ: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)

def main():
    root = tk.Tk()
    app = SimpleCSVCleaner(root)
    root.mainloop()

if __name__ == "__main__":
    main()

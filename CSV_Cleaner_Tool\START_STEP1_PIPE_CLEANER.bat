@echo off
title STEP 1 - Pipe Data Cleaner

echo ========================================
echo    STEP 1 - PIPE DATA CLEANER
echo ========================================
echo.
echo Starting Data Cleaning with Pipe Delimiter...
echo.
echo Features:
echo - Replaces "," with "|"
echo - <PERSON>les multiple commas (,,, → |)
echo - Handles merged data intelligently
echo - Outputs clean files ready for database
echo.

python STEP1_PIPE_CLEANER.py

if errorlevel 1 (
    echo.
    echo Failed to start Pipe Data Cleaner
    echo Please make sure Python is installed
    pause
)

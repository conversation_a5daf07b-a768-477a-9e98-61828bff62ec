#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 تثبيت المكتبات المطلوبة لتحسين أداء البحث
Install Required Libraries for Enhanced Search Performance
"""

import subprocess
import sys
import os

def install_package(package_name, description):
    """تثبيت مكتبة واحدة"""
    try:
        print(f"📦 تثبيت {description}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ تم تثبيت {description} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {description}: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع في تثبيت {description}: {e}")
        return False

def check_package(package_name):
    """فحص ما إذا كانت المكتبة مثبتة"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """تثبيت جميع المكتبات المطلوبة"""
    print("=" * 60)
    print("🚀 تثبيت المكتبات المطلوبة لتحسين أداء البحث")
    print("=" * 60)
    
    # قائمة المكتبات المطلوبة
    packages = [
        ("pandas", "pandas - مكتبة معالجة البيانات السريعة"),
        ("duckdb", "DuckDB - قاعدة بيانات تحليلية سريعة"),
        ("openpyxl", "openpyxl - دعم ملفات Excel"),
        ("xlsxwriter", "xlsxwriter - كتابة ملفات Excel محسنة")
    ]
    
    installed_count = 0
    total_count = len(packages)
    
    for package_name, description in packages:
        print(f"\n🔍 فحص {package_name}...")
        
        if check_package(package_name):
            print(f"✅ {description} مثبت بالفعل")
            installed_count += 1
        else:
            print(f"⚠️ {description} غير مثبت")
            if install_package(package_name, description):
                installed_count += 1
    
    print("\n" + "=" * 60)
    print("📊 ملخص التثبيت:")
    print(f"✅ تم تثبيت: {installed_count}/{total_count}")
    
    if installed_count == total_count:
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("🚀 يمكنك الآن الاستفادة من البحث المحسن")
    else:
        print("⚠️ بعض المكتبات لم يتم تثبيتها")
        print("💡 ستعمل الأداة بالطريقة التقليدية")
    
    print("=" * 60)
    
    # اختبار سريع للمكتبات
    print("\n🧪 اختبار المكتبات المثبتة:")
    
    try:
        import pandas as pd
        print("✅ pandas يعمل بشكل صحيح")
        print(f"   الإصدار: {pd.__version__}")
    except ImportError:
        print("❌ pandas غير متوفر")
    
    try:
        import duckdb
        print("✅ DuckDB يعمل بشكل صحيح")
        print(f"   الإصدار: {duckdb.__version__}")
    except ImportError:
        print("❌ DuckDB غير متوفر")
    
    print("\n💡 نصائح لتحسين الأداء:")
    print("1. استخدم ملفات CSV منظمة مع فاصل | (أنبوب)")
    print("2. تأكد من وجود مساحة كافية على القرص")
    print("3. استخدم الإيقاف المؤقت عند الحاجة لتوفير الذاكرة")
    print("4. احفظ النتائج بشكل دوري لتجنب فقدان البيانات")
    
    input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()

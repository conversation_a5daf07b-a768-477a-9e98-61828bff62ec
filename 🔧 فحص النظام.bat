@echo off
chcp 65001 >nul
title System Check - فحص النظام

echo.
echo ========================================
echo    🔧 System Check Tool
echo    أداة فحص النظام والجاهزية
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

REM Check if system check file exists
if not exist "system_check.py" (
    echo ❌ خطأ: ملف system_check.py غير موجود
    pause
    exit /b 1
)

echo 🚀 جاري تشغيل أداة فحص النظام...
echo.

REM Install required packages if needed
echo 📦 التحقق من المكتبات المطلوبة...
python -c "import psutil" 2>nul
if errorlevel 1 (
    echo 📥 تثبيت مكتبة psutil...
    pip install psutil
    if errorlevel 1 (
        echo ⚠️ تحذير: فشل في تثبيت psutil - سيتم تشغيل فحص محدود
    )
)

REM Run the system checker
python system_check.py

echo.
echo ✅ تم إغلاق أداة فحص النظام
pause

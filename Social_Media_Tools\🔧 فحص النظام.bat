@echo off
chcp 65001 >nul
title System Check - فحص النظام

echo.
echo ========================================
echo    🔧 System Check Tool
echo    أداة فحص النظام والجاهزية
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متاح
python --version
echo.

REM Check if system check file exists
if not exist "system_check.py" (
    echo ❌ خطأ: ملف system_check.py غير موجود
    echo 📁 تأكد من وجود جميع الملفات في المجلد
    pause
    exit /b 1
)

echo 🚀 جاري تشغيل أداة فحص النظام...
echo.

REM Install required packages if needed
echo 📦 التحقق من المكتبات المطلوبة...
python -c "import psutil" 2>nul
if errorlevel 1 (
    echo 📥 تثبيت مكتبة psutil...
    pip install psutil
    if errorlevel 1 (
        echo ⚠️ تحذير: فشل في تثبيت psutil - سيتم تشغيل فحص محدود
        echo 💡 يمكنك تثبيتها يدوياً: pip install psutil
    )
)

REM Run the system checker
python system_check.py

REM Check if the script ran successfully
if errorlevel 1 (
    echo.
    echo ❌ خطأ: فشل في تشغيل أداة فحص النظام
    echo.
    echo 🔧 حلول مقترحة:
    echo    • تأكد من تثبيت Python بشكل صحيح
    echo    • تأكد من وجود جميع الملفات المطلوبة
    echo    • جرب تشغيل الأمر: python system_check.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق أداة فحص النظام بنجاح
)

pause

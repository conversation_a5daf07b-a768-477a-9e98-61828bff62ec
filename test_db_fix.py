#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة عدم ظهور قواعد البيانات
"""

import tkinter as tk
from txt_to_csv_converter import ConversionApp

def test_database_display():
    """اختبار عرض قواعد البيانات"""
    print("🧪 بدء اختبار عرض قواعد البيانات...")
    
    # إنشاء نافذة اختبار
    root = tk.Tk()
    app = ConversionApp(root)
    
    # طباعة معلومات التشخيص
    print(f"📊 عدد قواعد البيانات المحددة: {len(app.selected_dbs)}")
    print(f"📋 قائمة قواعد البيانات: {app.selected_dbs}")
    
    # التحقق من وجود db_listbox
    if hasattr(app, 'db_listbox'):
        print("✅ تم العثور على db_listbox")
        print(f"📝 عدد العناصر في ListBox: {app.db_listbox.size()}")
        
        # طباعة محتويات ListBox
        items = []
        for i in range(app.db_listbox.size()):
            items.append(app.db_listbox.get(i))
        print(f"📄 محتويات ListBox: {items}")
    else:
        print("❌ لم يتم العثور على db_listbox")
    
    # اختبار إضافة قاعدة بيانات وهمية
    print("\n🔧 اختبار إضافة قاعدة بيانات وهمية...")
    
    # إضافة قاعدة بيانات وهمية للاختبار
    test_db_path = "test_database.csv"
    if test_db_path not in app.selected_dbs:
        app.selected_dbs.append(test_db_path)
        display_name = "test_database.csv"
        app.db_listbox.insert(tk.END, display_name)
        print(f"✅ تم إضافة قاعدة البيانات الوهمية: {display_name}")
    
    # التحقق مرة أخرى
    print(f"📊 عدد قواعد البيانات بعد الإضافة: {len(app.selected_dbs)}")
    print(f"📝 عدد العناصر في ListBox بعد الإضافة: {app.db_listbox.size()}")
    
    # طباعة محتويات ListBox بعد الإضافة
    items_after = []
    for i in range(app.db_listbox.size()):
        items_after.append(app.db_listbox.get(i))
    print(f"📄 محتويات ListBox بعد الإضافة: {items_after}")
    
    # تشغيل الواجهة لفترة قصيرة للاختبار
    print("\n🖥️ تشغيل الواجهة للاختبار...")
    print("💡 يمكنك الآن اختبار إضافة قواعد البيانات يدوياً")
    print("⚠️ أغلق النافذة عند الانتهاء من الاختبار")
    
    root.mainloop()

if __name__ == "__main__":
    test_database_display()

@echo off
chcp 65001 >nul
title Social Media Data Tools - أدوات البيانات الاجتماعية

echo.
echo ========================================
echo    🚀 Social Media Data Tools
echo    أدوات البيانات الاجتماعية
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo.
    echo 📥 يرجى تثبيت Python 3.6+ من:
    echo    https://python.org
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version
echo.

REM Check if required files exist
if not exist "main_launcher.py" (
    echo ❌ خطأ: ملف main_launcher.py غير موجود
    echo.
    echo 📁 تأكد من وجود جميع الملفات في نفس المجلد:
    echo    • main_launcher.py
    echo    • csv_cleaner_tool.py  
    echo    • txt_to_csv_converter.py
    echo.
    pause
    exit /b 1
)

echo 🚀 جاري تشغيل منصة الأدوات...
echo.

REM Run the main launcher
python main_launcher.py

REM Check if the script ran successfully
if errorlevel 1 (
    echo.
    echo ❌ خطأ: فشل في تشغيل منصة الأدوات
    echo.
    echo 🔧 حلول مقترحة:
    echo    • تأكد من تثبيت Python بشكل صحيح
    echo    • تأكد من وجود جميع الملفات المطلوبة
    echo    • جرب تشغيل الأمر: python main_launcher.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق منصة الأدوات بنجاح
)

pause

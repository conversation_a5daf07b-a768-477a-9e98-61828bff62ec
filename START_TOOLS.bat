@echo off
title Social Media Data Tools

echo.
echo ========================================
echo    Social Media Data Tools
echo ========================================
echo.
echo Opening tools folders...
echo.

REM Check and open CSV Cleaner Tool
if exist "CSV_Cleaner_Tool" (
    echo [OK] CSV Cleaner Tool found
    start "" "CSV_Cleaner_Tool"
) else (
    echo [ERROR] CSV_Cleaner_Tool folder not found
)

REM Check and open Data Search Tool
if exist "Data_Search_Tool" (
    echo [OK] Data Search Tool found
    start "" "Data_Search_Tool"
) else (
    echo [ERROR] Data_Search_Tool folder not found
)

echo.
echo ========================================
echo    Usage Instructions
echo ========================================
echo.
echo 1. CSV Cleaning:
echo    - Go to CSV_Cleaner_Tool folder
echo    - Double-click: run_csv_cleaner.bat
echo.
echo 2. Data Search:
echo    - Go to Data_Search_Tool folder  
echo    - Double-click: run_data_search.bat
echo.
echo ========================================
echo    Recommended Workflow
echo ========================================
echo.
echo Step 1: Clean your messy CSV files first
echo         (Use CSV Cleaner Tool)
echo.
echo Step 2: Search in the cleaned data
echo         (Use Data Search Tool)
echo.
echo This will give you faster and more accurate results!
echo.

pause

# 🔍 أداة البحث في البيانات الضخمة - Data Search Tool

## 📋 وصف البرنامج

هذا البرنامج هو أداة متقدمة للبحث والفلترة في قواعد البيانات الضخمة التي تحتوي على بيانات شخصية. يدعم البرنامج ملفات بأحجام تتجاوز 11 جيجابايت ويمكنه التعامل مع صيغ متعددة.

## ✨ المميزات الرئيسية

- 📊 **دعم صيغ متعددة**: TXT, CSV, SQLite DB
- 🚀 **معالجة الملفات الضخمة**: يدعم ملفات تتجاوز 11 GB
- 🔍 **بحث متقدم**: بحث بالمعرفات والكلمات المفتاحية
- 🎯 **بحث مخصص**: text box لكل نوع بيانات للبحث المحدد
- ⚡ **أداء عالي**: معالجة سريعة مع شريط تقدم تفصيلي
- 🌐 **دعم متعدد اللغات**: بحث بالعربية والإنجليزية
- 🎯 **تصفية ذكية**: اختيار البيانات المطلوبة فقط
- 💾 **نتائج منظمة**: حفظ النتائج في ملف CSV مع رؤوس أعمدة

## 🛠️ كيفية الاستخدام

### 1. تشغيل البرنامج
```bash
python run_app.py
```

### 2. إعداد قواعد البيانات
- اضغط على "➕ إضافة قاعدة بيانات"
- اختر ملف أو عدة ملفات (TXT/CSV/DB)
- يمكن إضافة عدة قواعد بيانات في نفس الوقت

### 3. تحديد ملف المعرفات
- اختر ملف نصي يحتوي على المعرفات أو الكلمات المفتاحية
- كل معرف في سطر منفصل
- مثال:
```
100001234567890
<EMAIL>
+1234567890
```

### 4. اختيار البيانات المطلوبة والبحث المتقدم
- حدد البيانات التي تريد ظهورها في النتائج
- **ميزة جديدة**: يمكنك إضافة كلمات بحث محددة لكل نوع بيانات:

#### أمثلة على البحث المتقدم:
- **Facebook ID**: `100001234567890`
- **الاسم**: `أحمد محمد` أو `Ahmed Mohamed`
- **البريد الإلكتروني**: `gmail.com` أو `yahoo.com`
- **رقم الهاتف**: `+20` أو `010` أو `011`
- **الموقع**: `القاهرة` أو `Cairo` أو `المعادي`
- **العمر**: `25` أو `30-40`
- **الجنس**: `ذكر` أو `أنثى` أو `Male` أو `Female`
- **الحالة الاجتماعية**: `متزوج` أو `أعزب` أو `مطلق` أو `Married`
- **العمل**: `مهندس` أو `طبيب` أو `Engineer`
- **التعليم**: `جامعة القاهرة` أو `Cairo University`
- **الاهتمامات**: `كرة القدم` أو `Football` أو `موسيقى`

### 5. تحديد ملف النتائج
- اختر مكان حفظ النتائج (ملف CSV)

### 6. بدء البحث
- اضغط على "🚀 بدء البحث"
- راقب شريط التقدم التفصيلي الذي يعرض:
  - 📊 النسبة المئوية للتقدم
  - 🔢 عدد السجلات المعالجة
  - ✅ عدد النتائج المطابقة
  - 📝 حالة العملية الحالية
- انتظر حتى اكتمال العملية

## ⚙️ الخيارات المتقدمة

### خيارات الأداء
- **البحث السريع**: موصى به للملفات الكبيرة
- **حساس لحالة الأحرف**: للبحث الدقيق
- **مطابقة تامة فقط**: للنتائج المحددة

### البحث الإضافي
- يمكن إضافة كلمات بحث إضافية في حقل "بحث إضافي بالكلمات"

## 📁 هيكل الملفات

```
mytool/
├── txt_to_csv_converter.py    # الكود الرئيسي
├── run_app.py                 # ملف التشغيل
├── README.md                  # هذا الملف
├── test_converter.py          # ملف الاختبار
├── sample_data.csv            # بيانات تجريبية للاختبار
├── sample_search_ids.txt      # معرفات تجريبية للبحث
└── create_test_data.py        # إنشاء بيانات تجريبية كبيرة
```

## 🧪 ملفات الاختبار

تم توفير ملفات تجريبية لاختبار البرنامج:

### `sample_data.csv`
ملف يحتوي على 10 سجلات تجريبية بتنسيق CSV مع بيانات متنوعة

### `sample_search_ids.txt`
ملف يحتوي على معرفات وكلمات مفتاحية للبحث:
- معرفات Facebook
- أسماء
- مواقع
- حالات اجتماعية
- نطاقات إيميل

### `create_test_data.py`
سكريبت لإنشاء ملف بيانات كبير (50,000 سجل) لاختبار الأداء وشريط التقدم:
```bash
pip install faker
python create_test_data.py
```

## 📊 شريط التقدم التفصيلي

يعرض البرنامج شريط تقدم متقدم يوضح:

### 📈 المعلومات المعروضة:
- **النسبة المئوية**: التقدم الإجمالي للعملية
- **السجلات المعالجة**: عدد السجلات التي تمت معالجتها
- **النتائج المطابقة**: عدد السجلات التي تطابقت مع معايير البحث
- **الحالة الحالية**: وصف تفصيلي للعملية الجارية

### 🔄 مراحل العملية:
1. **حساب البيانات** (0-20%): حساب إجمالي السجلات
2. **المعالجة** (20-100%): البحث والفلترة
3. **الإنجاز** (100%): عرض النتائج النهائية

### ⏹️ التحكم في العملية:
- **إيقاف**: يمكن إيقاف العملية في أي وقت
- **تحديث مباشر**: تحديث المعلومات كل 1000 سجل

## 🔧 المتطلبات

- Python 3.6+
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

## 🚨 نصائح مهمة

1. **الملفات الكبيرة**: استخدم "البحث السريع" للملفات أكبر من 1 GB
2. **الذاكرة**: تأكد من وجود ذاكرة كافية (RAM) للملفات الضخمة
3. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من بياناتك
4. **الصبر**: العمليات على الملفات الكبيرة تحتاج وقت

## 🐛 حل المشاكل

### البرنامج لا يستجيب
- انتظر قليلاً، قد يكون يعالج ملف كبير
- تحقق من مساحة القرص الصلب

### خطأ في قراءة الملف
- تأكد من صيغة الملف (TXT/CSV/DB)
- تحقق من ترميز الملف (UTF-8 مفضل)

### نتائج قليلة أو لا توجد نتائج
- تحقق من ملف المعرفات
- جرب إلغاء "مطابقة تامة فقط"
- تأكد من صحة المعرفات

## 📞 الدعم

إذا واجهت أي مشاكل، تأكد من:
1. تحديث Python إلى أحدث إصدار
2. التحقق من صحة ملفات البيانات
3. إعادة تشغيل البرنامج

---
**تم تطوير هذا البرنامج لأغراض البحث والتحليل المشروع للبيانات**

@echo off
title CSV Cleaner Tool

echo ========================================
echo    CSV Cleaner Tool
echo ========================================
echo.
echo Starting CSV Cleaner...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.6+ from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Check if the Python file exists
if not exist "csv_cleaner.py" (
    echo ERROR: csv_cleaner.py file not found
    echo.
    echo Make sure you are in the correct directory
    echo.
    pause
    exit /b 1
)

REM Run the CSV cleaner
python csv_cleaner.py

REM Check if there was an error
if errorlevel 1 (
    echo.
    echo ERROR: Failed to run CSV Cleaner
    echo.
    pause
) else (
    echo.
    echo CSV Cleaner closed successfully
)

pause

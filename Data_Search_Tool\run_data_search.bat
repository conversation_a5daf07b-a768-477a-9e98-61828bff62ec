@echo off
title Data Search Tool

echo ========================================
echo    Data Search Tool
echo ========================================
echo.
echo Starting Data Search Tool...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.6+ from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Check if the Python file exists
if not exist "data_search.py" (
    echo ERROR: data_search.py file not found
    echo.
    echo Make sure you are in the correct directory
    echo.
    pause
    exit /b 1
)

REM Run the data search tool
python data_search.py

REM Check if there was an error
if errorlevel 1 (
    echo.
    echo ERROR: Failed to run Data Search Tool
    echo.
    pause
) else (
    echo.
    echo Data Search Tool closed successfully
)

pause

import csv
import os
import sys
import time
import sqlite3
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread, Event

# زيادة الحد الأقصى لحجم الحقل في CSV للتعامل مع البيانات الكبيرة
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class ConversionApp:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🔍 From SM To SS - Social Media Data Search Tool")
        self.root.geometry("1200x800")
        self.root.configure(pady=10, padx=10)

        # إنشاء متغير للتحكم في إيقاف المعالجة
        self.stop_event = Event()

        # تهيئة المتغيرات الأساسية
        self.init_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()

    def init_variables(self):
        """تهيئة جميع المتغيرات المطلوبة"""
        # المتغيرات الأساسية
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.fb_ids_file = tk.StringVar()
        self.delimiter = tk.StringVar(value=',')
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        
        # متغيرات المعالجة المحسنة للبيانات الضخمة
        self.batch_size = 50000  # حجم دفعة أكبر للأداء الأفضل
        self.chunk_size = 65536  # حجم قراءة أكبر (64KB)
        self.lines_per_file = 1000000  # مليون سطر لكل ملف
        self.update_frequency = 10000  # تحديث الواجهة كل 10,000 سجل
        self.fb_ids = set()  # مجموعة معرفات Facebook
        self.processing = False  # حالة المعالجة
        self.selected_dbs = []  # قائمة قواعد البيانات المحددة
        self.total_records = 0  # إجمالي عدد السجلات
        self.current_row = []  # السجل الحالي
        self.current_criteria = {}  # معايير البحث الحالية
        self.db_type = 'csv'  # نوع قاعدة البيانات الافتراضي
        
        # متغيرات البحث
        self.search_criteria = {}

        # تعريف المتغيرات الأساسية
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.fb_ids_file = tk.StringVar()
        self.delimiter = tk.StringVar(value=',')
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز...")
        self.fb_ids = set()
        
        # تعريف متغيرات البحث والفلترة (تم نقلها للأعلى لتجنب التكرار)
        self.criteria = {}
        self.total_records = 0
        
        # تعريف حقول الإخراج
        self.output_fields = {
            'Facebook ID': tk.BooleanVar(value=True),
            'First Name': tk.BooleanVar(value=True),
            'Last Name': tk.BooleanVar(value=True),
            'Email': tk.BooleanVar(value=True),
            'Phone': tk.BooleanVar(value=True),
            'Gender': tk.BooleanVar(value=True),
            'Birthday': tk.BooleanVar(value=True),
            'Location': tk.BooleanVar(value=True),
            'Username': tk.BooleanVar(value=True),
            'Profile URL': tk.BooleanVar(value=True),
            'Education': tk.BooleanVar(value=True),
            'Work': tk.BooleanVar(value=True),
            'Religion': tk.BooleanVar(value=True),
            'Relationship': tk.BooleanVar(value=True),
            'About Me': tk.BooleanVar(value=True)
        }
        
        # تعريف قيم البحث
        self.search_values = {
            'Facebook ID': tk.StringVar(),
            'Phone': tk.StringVar(),
            'First Name': tk.StringVar(),
            'Last Name': tk.StringVar(),
            'Email': tk.StringVar(),
            'Location': tk.StringVar(),
            'Birthday': tk.StringVar(),
            'Gender': tk.StringVar(value='all'),
            'Religion': tk.StringVar(),
            'Education': tk.StringVar(),
            'Work': tk.StringVar(),
            'Relationship': tk.StringVar()
        }

        # تعريف خيارات الفلترة
        self.filter_options = {
            'id': tk.BooleanVar(value=False),
            'phone': tk.BooleanVar(value=False),
            'name': tk.BooleanVar(value=False),
            'gender': tk.BooleanVar(value=False),
            'email': tk.BooleanVar(value=False)
        }
        
        self.filter_values = {
            'id': tk.StringVar(),
            'phone': tk.StringVar(),
            'name': tk.StringVar(),
            'gender': tk.StringVar(),
            'email': tk.StringVar()
        }
        
        # تعريف متغيرات إضافية
        self.gender_var = tk.StringVar(value="all")
        self.keywords = tk.StringVar()
        self.region = tk.StringVar()
        self.use_keywords = tk.BooleanVar(value=False)
        self.use_region = tk.BooleanVar(value=False)
        self.use_fbids = tk.BooleanVar(value=False)

        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الجديدة"""
        # الإطار الرئيسي مع تخطيط أفضل
        main_container = ttk.Frame(self.root)
        main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # الجزء العلوي - البحث
        self.create_search_section(main_container)

        # الجزء الأوسط - اختيار الحقول وقواعد البيانات
        self.create_middle_section(main_container)

        # الجزء السفلي - التحكم والتقدم
        self.create_bottom_section(main_container)

    def create_search_section(self, parent):
        """إنشاء قسم البحث العلوي"""
        search_frame = ttk.LabelFrame(parent, text="🔍 Search", padding=10)
        search_frame.pack(fill='x', pady=(0, 10))

        # تقسيم إلى عمودين
        columns_frame = ttk.Frame(search_frame)
        columns_frame.pack(fill='both', expand=True)

        # العمود الأيسر - البحث بالملف
        left_column = ttk.LabelFrame(columns_frame, text="Search By File", padding=10)
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # ملف البيانات
        ttk.Label(left_column, text="Data File:").pack(anchor='w')
        data_file_frame = ttk.Frame(left_column)
        data_file_frame.pack(fill='x', pady=5)
        self.data_file_entry = ttk.Entry(data_file_frame, textvariable=self.fb_ids_file)
        self.data_file_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(data_file_frame, text="📁", command=self.browse_fbids, width=3).pack(side='right')

        # البحث بواسطة
        ttk.Label(left_column, text="Search By:").pack(anchor='w', pady=(10, 0))
        search_by_frame = ttk.Frame(left_column)
        search_by_frame.pack(fill='x', pady=5)

        self.search_type = tk.StringVar(value="id")
        ttk.Radiobutton(search_by_frame, text="📱 ID", variable=self.search_type, value="id").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_by_frame, text="📞 Phone", variable=self.search_type, value="phone").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_by_frame, text="📧 Email", variable=self.search_type, value="email").pack(side='left')

        # زر البحث
        ttk.Button(left_column, text="🔍 Search", command=self.start_search,
                  style='Accent.TButton').pack(pady=(10, 0), fill='x')

        # العمود الأيمن - البحث بالقيم
        right_column = ttk.LabelFrame(columns_frame, text="Search By Value", padding=10)
        right_column.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # إنشاء حقول البحث
        self.create_search_fields(right_column)

    def create_search_fields(self, parent):
        """إنشاء حقول البحث بالقيم"""
        # قائمة الحقول
        fields = [
            ('Facebook ID', 'facebook_id'),
            ('Phone', 'phone'),
            ('First Name', 'first_name'),
            ('Last Name', 'last_name'),
            ('E-mail', 'email'),
            ('Birthday', 'birthday'),
            ('Birthday Year', 'birthday_year'),
            ('Locale', 'locale'),
            ('Hometown', 'hometown'),
            ('Work', 'work'),
            ('Country', 'country'),
            ('Education', 'education'),
            ('Relationship', 'relationship'),
            ('Religion', 'religion'),
            ('About Me', 'about_me'),
            ('Gender', 'gender'),
            ('Limit', 'limit')
        ]

        # إنشاء حقول الإدخال
        self.search_values = {}
        for i, (label, key) in enumerate(fields):
            if i < 8:  # العمود الأيسر
                row = i
                col = 0
            else:  # العمود الأيمن
                row = i - 8
                col = 2

            ttk.Label(parent, text=f"{label}:").grid(row=row, column=col, sticky='w', padx=(0, 5), pady=2)

            if key == 'gender':
                # قائمة منسدلة للجنس
                gender_var = tk.StringVar(value="All")
                self.search_values[key] = gender_var
                gender_combo = ttk.Combobox(parent, textvariable=gender_var,
                                          values=["All", "Male", "Female", "ذكر", "أنثى"],
                                          state="readonly", width=20)
                gender_combo.grid(row=row, column=col+1, sticky='ew', padx=(0, 10), pady=2)
            else:
                # حقل نص عادي
                var = tk.StringVar()
                self.search_values[key] = var
                entry = ttk.Entry(parent, textvariable=var, width=25)
                entry.grid(row=row, column=col+1, sticky='ew', padx=(0, 10), pady=2)

        # تكوين الأعمدة
        parent.columnconfigure(1, weight=1)
        parent.columnconfigure(3, weight=1)

        # زر البحث في الأسفل
        search_btn = ttk.Button(parent, text="🔍 Search", command=self.start_search)
        search_btn.grid(row=9, column=0, columnspan=4, pady=(10, 0), sticky='ew')

    def create_middle_section(self, parent):
        """إنشاء القسم الأوسط - اختيار الحقول وقواعد البيانات"""
        middle_frame = ttk.Frame(parent)
        middle_frame.pack(fill='both', expand=True, pady=(0, 10))

        # العمود الأيسر - اختيار حقول الإخراج
        left_frame = ttk.LabelFrame(middle_frame, text="Select Output Fields", padding=10)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # إنشاء checkboxes للحقول
        self.output_fields = {}
        fields = [
            ('Facebook ID', 'facebook_id'),
            ('Last Name', 'last_name'),
            ('Birthday Year', 'birthday_year'),
            ('Hometown', 'hometown'),
            ('Work', 'work'),
            ('Religion', 'religion'),
            ('Phone', 'phone'),
            ('E-mail', 'email'),
            ('Birthday', 'birthday'),
            ('Locale', 'locale'),
            ('Country', 'country'),
            ('Education', 'education'),
            ('First Name', 'first_name'),
            ('Gender', 'gender'),
            ('Location', 'location'),
            ('Relationship', 'relationship'),
            ('About Me', 'about_me')
        ]

        # ترتيب الحقول في 3 أعمدة
        for i, (label, key) in enumerate(fields):
            col = i % 3
            row = i // 3

            var = tk.BooleanVar(value=True if key in ['facebook_id', 'first_name', 'last_name'] else False)
            self.output_fields[key] = var

            cb = ttk.Checkbutton(left_frame, text=label, variable=var)
            cb.grid(row=row, column=col, sticky='w', padx=5, pady=2)

        # العمود الأيمن - قواعد البيانات
        right_frame = ttk.LabelFrame(middle_frame, text="Select Database", padding=10)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # قائمة قواعد البيانات مع scrollbar
        db_frame = ttk.Frame(right_frame)
        db_frame.pack(fill='both', expand=True, pady=(0, 10))

        self.db_listbox = tk.Listbox(
            db_frame,
            selectmode='multiple',
            height=8,
            font=('Arial', 10),
            bg='white',
            fg='black',
            selectbackground='#0078d4',
            selectforeground='white'
        )

        # إضافة scrollbar
        scrollbar = ttk.Scrollbar(db_frame, orient='vertical', command=self.db_listbox.yview)
        self.db_listbox.configure(yscrollcommand=scrollbar.set)

        # ترتيب العناصر
        self.db_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # إضافة نص توضيحي إذا كانت القائمة فارغة
        self.db_listbox.insert(0, "لا توجد قواعد بيانات محددة - انقر 'Add Database' لإضافة قواعد البيانات")
        self.db_listbox.config(fg='gray')

        print(f"🔧 تم إنشاء db_listbox بنجاح - العناصر الحالية: {self.db_listbox.size()}")

        # أزرار إدارة قواعد البيانات
        db_buttons_frame = ttk.Frame(right_frame)
        db_buttons_frame.pack(fill='x')

        ttk.Button(db_buttons_frame, text="➕ Add Database",
                  command=self.add_database).pack(side='left', padx=(0, 5))
        ttk.Button(db_buttons_frame, text="➖ Remove",
                  command=self.remove_database).pack(side='left', padx=(0, 5))
        ttk.Button(db_buttons_frame, text="🗑️ Clear All",
                  command=self.clear_databases).pack(side='left')

    def create_bottom_section(self, parent):
        """إنشاء القسم السفلي - التحكم والتقدم"""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.pack(fill='x')

        # أزرار التحكم
        control_frame = ttk.Frame(bottom_frame)
        control_frame.pack(fill='x', pady=(0, 10))

        # الأزرار الرئيسية
        self.start_button = ttk.Button(control_frame, text="🚀 Start Search",
                                      command=self.start_search, style='Accent.TButton')
        self.start_button.pack(side='left', padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="⏹️ Stop",
                                     command=self.stop_processing, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))

        ttk.Button(control_frame, text="📁 Open Results",
                  command=self.open_results_folder).pack(side='left', padx=(0, 10))

        ttk.Button(control_frame, text="💾 Save As",
                  command=self.browse_output).pack(side='left')

        # شريط التقدم المحسن
        self.create_enhanced_progress_bar(bottom_frame)

    def create_enhanced_progress_bar(self, parent):
        """إنشاء شريط تقدم محسن"""
        progress_frame = ttk.LabelFrame(parent, text="📊 Progress", padding=10)
        progress_frame.pack(fill='x')

        # شريط التقدم الرئيسي
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress,
                                           maximum=100, mode='determinate', length=500)
        self.progress_bar.pack(fill='x', pady=(0, 10))

        # معلومات التقدم
        info_frame = ttk.Frame(progress_frame)
        info_frame.pack(fill='x')

        # النسبة المئوية
        ttk.Label(info_frame, text="Progress:").pack(side='left')
        self.progress_percentage = tk.StringVar(value="0%")
        ttk.Label(info_frame, textvariable=self.progress_percentage,
                 font=('Arial', 10, 'bold'), foreground='blue').pack(side='left', padx=(5, 20))

        # السجلات المعالجة
        ttk.Label(info_frame, text="Processed:").pack(side='left')
        self.processed_count = tk.StringVar(value="0")
        ttk.Label(info_frame, textvariable=self.processed_count,
                 font=('Arial', 10, 'bold')).pack(side='left', padx=(5, 20))

        # النتائج المطابقة
        ttk.Label(info_frame, text="Matches:").pack(side='left')
        self.matches_count = tk.StringVar(value="0")
        ttk.Label(info_frame, textvariable=self.matches_count,
                 font=('Arial', 10, 'bold'), foreground='green').pack(side='left', padx=(5, 0))

        # حالة العملية
        status_frame = ttk.Frame(progress_frame)
        status_frame.pack(fill='x', pady=(10, 0))

        ttk.Label(status_frame, text="Status:").pack(side='left')
        self.status_label = ttk.Label(status_frame, textvariable=self.status,
                                     foreground='navy')
        self.status_label.pack(side='left', padx=(5, 0))

    def create_file_frame(self, parent):
        """إنشاء إطار اختيار الملفات"""
        file_frame = ttk.LabelFrame(parent, text="📁 ملفات البيانات والإعدادات", padding=10)
        file_frame.pack(fill='x', padx=5, pady=5)



        # ملف المعرفات/الكلمات المفتاحية
        search_file_frame = ttk.Frame(file_frame)
        search_file_frame.pack(fill='x', pady=5)
        ttk.Label(search_file_frame, text="🔍 ملف المعرفات/الكلمات المفتاحية:").pack(anchor='w')
        entry_frame = ttk.Frame(search_file_frame)
        entry_frame.pack(fill='x', pady=2)
        ttk.Entry(entry_frame, textvariable=self.fb_ids_file).pack(side='left', fill='x', expand=True, padx=(0,5))
        ttk.Button(entry_frame, text="تصفح", command=self.browse_fbids).pack(side='right')

        # ملف الإخراج
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Label(output_frame, text="💾 ملف النتائج (CSV):").pack(anchor='w')
        entry_frame2 = ttk.Frame(output_frame)
        entry_frame2.pack(fill='x', pady=2)
        ttk.Entry(entry_frame2, textvariable=self.output_path).pack(side='left', fill='x', expand=True, padx=(0,5))
        ttk.Button(entry_frame2, text="تصفح", command=self.browse_output).pack(side='right')

    def create_search_frame(self, parent):
        """إنشاء إطار البحث والتصفية"""
        search_frame = ttk.LabelFrame(parent, text="⚙️ خيارات البحث والتصفية", padding=10)
        search_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # إطار رئيسي بعمودين
        main_search_frame = ttk.Frame(search_frame)
        main_search_frame.pack(fill='both', expand=True)

        # العمود الأيسر - خيارات البيانات المطلوبة
        left_frame = ttk.LabelFrame(main_search_frame, text="📋 البيانات المطلوبة في النتائج", padding=5)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0,5))

        # إنشاء checkboxes والـ text boxes للبيانات المطلوبة
        self.output_options = {}
        self.search_filters = {}  # للبحث في حقول محددة

        data_types = [
            ('facebook_id', 'Facebook ID', 'مثال: 100001234567890'),
            ('name', 'الاسم', 'مثال: أحمد محمد أو Ahmed Mohamed'),
            ('email', 'البريد الإلكتروني', 'مثال: gmail.com أو yahoo.com'),
            ('phone', 'رقم الهاتف', 'مثال: +20 أو 010 أو 011'),
            ('location', 'الموقع', 'مثال: القاهرة أو Cairo أو المعادي'),
            ('age', 'العمر', 'مثال: 25 أو 30-40'),
            ('gender', 'الجنس', 'مثال: ذكر أو أنثى أو Male أو Female'),
            ('relationship', 'الحالة الاجتماعية', 'مثال: متزوج أو أعزب أو مطلق أو Married'),
            ('work', 'العمل', 'مثال: مهندس أو طبيب أو Engineer'),
            ('education', 'التعليم', 'مثال: جامعة القاهرة أو Cairo University'),
            ('interests', 'الاهتمامات', 'مثال: كرة القدم أو Football أو موسيقى'),
            ('bio', 'السيرة الذاتية/الوصف', 'مثال: كلمات في الوصف الشخصي'),
            ('all_data', 'جميع البيانات المتاحة', '')
        ]

        for key, label, placeholder in data_types:
            # إطار لكل عنصر
            item_frame = ttk.Frame(left_frame)
            item_frame.pack(fill='x', pady=2)

            # Checkbox للتفعيل/إلغاء التفعيل
            var = tk.BooleanVar(value=True if key in ['facebook_id', 'name'] else False)
            self.output_options[key] = var

            checkbox = ttk.Checkbutton(item_frame, text=label, variable=var, width=20)
            checkbox.pack(side='left', padx=(0, 5))

            # Text box للبحث (إلا للعنصر الأخير)
            if key != 'all_data':
                search_var = tk.StringVar()
                self.search_filters[key] = search_var

                entry = ttk.Entry(item_frame, textvariable=search_var, width=25)
                entry.pack(side='left', fill='x', expand=True, padx=(0, 5))

                # إضافة placeholder text
                entry.insert(0, placeholder)
                entry.config(foreground='gray')

                # وظائف للـ placeholder
                def on_focus_in(event, entry=entry, placeholder=placeholder):
                    if entry.get() == placeholder:
                        entry.delete(0, tk.END)
                        entry.config(foreground='black')

                def on_focus_out(event, entry=entry, placeholder=placeholder):
                    if not entry.get():
                        entry.insert(0, placeholder)
                        entry.config(foreground='gray')

                entry.bind('<FocusIn>', on_focus_in)
                entry.bind('<FocusOut>', on_focus_out)

        # العمود الأيمن - خيارات البحث
        right_frame = ttk.LabelFrame(main_search_frame, text="🔍 خيارات البحث المتقدم", padding=5)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5,0))

        # خيار البحث بالكلمات المفتاحية
        ttk.Label(right_frame, text="🔤 بحث إضافي بالكلمات:").pack(anchor='w', pady=2)
        self.search_entry = ttk.Entry(right_frame, width=30)
        self.search_entry.pack(fill='x', pady=2)
        ttk.Label(right_frame, text="(اختياري - للبحث في جميع الحقول)", font=('Arial', 8)).pack(anchor='w')

        # خيارات التصفية
        ttk.Separator(right_frame, orient='horizontal').pack(fill='x', pady=10)
        ttk.Label(right_frame, text="⚡ خيارات الأداء:").pack(anchor='w', pady=2)

        self.use_fast_search = tk.BooleanVar(value=True)
        ttk.Checkbutton(right_frame, text="البحث السريع (موصى به)", variable=self.use_fast_search).pack(anchor='w')

        self.case_sensitive = tk.BooleanVar(value=False)
        ttk.Checkbutton(right_frame, text="حساس لحالة الأحرف", variable=self.case_sensitive).pack(anchor='w')

        self.exact_match = tk.BooleanVar(value=False)
        ttk.Checkbutton(right_frame, text="مطابقة تامة فقط", variable=self.exact_match).pack(anchor='w')

    def add_database(self):
        """إضافة قاعدة بيانات جديدة"""
        filetypes = [
            ("جميع الملفات المدعومة", "*.txt;*.csv;*.db"),
            ("ملفات نصية", "*.txt"),
            ("ملفات CSV", "*.csv"),
            ("قواعد بيانات SQLite", "*.db"),
            ("جميع الملفات", "*.*")
        ]

        filenames = filedialog.askopenfilenames(
            title="اختر قواعد البيانات",
            filetypes=filetypes
        )

        if filenames:
            # مسح النص التوضيحي إذا كان موجوداً
            if self.db_listbox.size() == 1:
                first_item = self.db_listbox.get(0)
                if "لا توجد قواعد بيانات" in first_item:
                    self.db_listbox.delete(0)
                    self.db_listbox.config(fg='black')

            added_count = 0
            for filename in filenames:
                if filename and filename not in self.selected_dbs:
                    self.selected_dbs.append(filename)
                    # إضافة اسم الملف فقط للعرض
                    display_name = os.path.basename(filename)
                    self.db_listbox.insert(tk.END, display_name)
                    added_count += 1
                    print(f"✅ تم إضافة قاعدة البيانات: {display_name}")  # للتشخيص

                    # فرض تحديث الواجهة
                    self.db_listbox.update()
                    self.root.update_idletasks()

            if added_count > 0:
                # تحديث معلومات الحالة
                self.update_status_info()

                # تأكيد للمستخدم
                messagebox.showinfo("نجح", f"تم إضافة {added_count} قاعدة بيانات بنجاح")

                # طباعة حالة ListBox للتشخيص
                print(f"📊 عدد العناصر في ListBox: {self.db_listbox.size()}")
                print(f"📋 محتويات ListBox:")
                for i in range(self.db_listbox.size()):
                    print(f"  {i+1}. {self.db_listbox.get(i)}")
            else:
                messagebox.showinfo("تنبيه", "جميع قواعد البيانات المحددة موجودة بالفعل")
        else:
            print("❌ لم يتم اختيار أي ملفات")  # للتشخيص

    def remove_database(self):
        """حذف قاعدة البيانات المحددة"""
        selected_indices = self.db_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("تنبيه", "يرجى اختيار قاعدة بيانات لحذفها")
            return

        # حذف من الخلف للأمام لتجنب مشاكل الفهرسة
        for index in reversed(selected_indices):
            self.db_listbox.delete(index)
            if index < len(self.selected_dbs):
                del self.selected_dbs[index]

        self.update_status_info()

    def clear_databases(self):
        """مسح جميع قواعد البيانات"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع قواعد البيانات؟"):
            self.db_listbox.delete(0, tk.END)
            self.selected_dbs.clear()

            # إضافة النص التوضيحي مرة أخرى
            self.db_listbox.insert(0, "لا توجد قواعد بيانات محددة - انقر 'Add Database' لإضافة قواعد البيانات")
            self.db_listbox.config(fg='gray')

            self.update_status_info()
            print("🗑️ تم مسح جميع قواعد البيانات")

    def update_status_info(self):
        """تحديث معلومات الحالة"""
        db_count = len(self.selected_dbs)
        if db_count == 0:
            self.status.set("لم يتم اختيار أي قاعدة بيانات")
        else:
            total_size = 0
            for db_path in self.selected_dbs:
                try:
                    total_size += os.path.getsize(db_path)
                except:
                    pass

            size_gb = total_size / (1024**3)
            self.status.set(f"تم اختيار {db_count} قاعدة بيانات - الحجم الإجمالي: {size_gb:.2f} GB")

    def create_progress_frame(self, parent):
        """إنشاء إطار التقدم والحالة"""
        progress_frame = ttk.LabelFrame(parent, text="📊 حالة التقدم", padding=10)
        progress_frame.pack(fill='x', padx=5, pady=5)

        # إطار النسبة المئوية
        percentage_frame = ttk.Frame(progress_frame)
        percentage_frame.pack(fill='x', pady=(0, 5))

        ttk.Label(percentage_frame, text="التقدم:", font=('Arial', 9, 'bold')).pack(side='left')
        self.progress_percentage = tk.StringVar(value="0%")
        ttk.Label(percentage_frame, textvariable=self.progress_percentage,
                 font=('Arial', 9, 'bold'), foreground='blue').pack(side='right')

        # شريط التقدم الرئيسي
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress,
            maximum=100,
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(fill='x', pady=5)

        # إطار معلومات التقدم التفصيلية
        details_frame = ttk.Frame(progress_frame)
        details_frame.pack(fill='x', pady=(5, 0))

        # السجلات المعالجة
        ttk.Label(details_frame, text="السجلات المعالجة:", font=('Arial', 8)).pack(side='left')
        self.processed_count = tk.StringVar(value="0")
        ttk.Label(details_frame, textvariable=self.processed_count,
                 font=('Arial', 8, 'bold')).pack(side='left', padx=(5, 20))

        # النتائج المطابقة
        ttk.Label(details_frame, text="النتائج المطابقة:", font=('Arial', 8)).pack(side='left')
        self.matches_count = tk.StringVar(value="0")
        ttk.Label(details_frame, textvariable=self.matches_count,
                 font=('Arial', 8, 'bold'), foreground='green').pack(side='left', padx=(5, 0))

        # حالة العملية
        status_frame = ttk.Frame(progress_frame)
        status_frame.pack(fill='x', pady=(10, 0))

        ttk.Label(status_frame, text="الحالة:", font=('Arial', 9, 'bold')).pack(anchor='w')
        self.status_label = ttk.Label(
            status_frame,
            textvariable=self.status,
            wraplength=800,
            font=('Arial', 9),
            foreground='navy'
        )
        self.status_label.pack(anchor='w', pady=(2, 0))

    def create_control_frame(self, parent):
        """إنشاء إطار أزرار التحكم"""
        control_frame = ttk.LabelFrame(parent, text="🎮 التحكم في العملية", padding=10)
        control_frame.pack(fill='x', padx=5, pady=5)

        # الأزرار الرئيسية
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x')

        # زر البدء
        self.start_button = ttk.Button(
            buttons_frame,
            text="🚀 بدء البحث",
            command=self.start_search,
            style='Accent.TButton'
        )
        self.start_button.pack(side='left', padx=5)

        # زر الإيقاف
        self.stop_button = ttk.Button(
            buttons_frame,
            text="⏹️ إيقاف",
            command=self.stop_processing,
            state='disabled'
        )
        self.stop_button.pack(side='left', padx=5)

        # زر مسح النتائج
        ttk.Button(
            buttons_frame,
            text="🗑️ مسح النتائج",
            command=self.clear_results
        ).pack(side='left', padx=5)

        # زر فتح مجلد النتائج
        ttk.Button(
            buttons_frame,
            text="📁 فتح مجلد النتائج",
            command=self.open_results_folder
        ).pack(side='left', padx=5)

        # معلومات سريعة
        info_frame = ttk.Frame(control_frame)
        info_frame.pack(fill='x', pady=(10,0))

        ttk.Label(info_frame, text="💡 نصائح:", font=('Arial', 9, 'bold')).pack(anchor='w')
        tips_text = "• يمكن اختيار عدة قواعد بيانات في نفس الوقت\n• ملف المعرفات يجب أن يحتوي على معرف واحد في كل سطر\n• البحث السريع موصى به للملفات الكبيرة"
        ttk.Label(info_frame, text=tips_text, font=('Arial', 8), foreground='gray').pack(anchor='w')

    def start_search(self):
        """بدء عملية البحث الجديدة"""
        # التحقق من الإعدادات الأساسية
        if not self.selected_dbs:
            messagebox.showwarning("Warning", "Please select at least one database")
            return

        # تحديد ملف الإخراج إذا لم يكن محدداً
        if not self.output_path.get():
            output_file = filedialog.asksaveasfilename(
                title="Save Results As",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            if not output_file:
                return
            self.output_path.set(output_file)

        # تحميل المعرفات إذا كان هناك ملف
        if self.fb_ids_file.get():
            self.load_fbids()

        # تحسينات للبيانات الضخمة
        if not self.optimize_for_large_data():
            return

        # تحديث حالة الأزرار
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # إعادة تعيين شريط التقدم
        self.reset_progress()

        # تجميع معايير البحث من الحقول الجديدة
        search_criteria = {}

        # البحث بالقيم من الحقول
        for key, var in self.search_values.items():
            value = var.get().strip()
            if value and value != "All":  # تجاهل القيم الفارغة و "All"
                search_criteria[key] = value

        # تحديد نوع البحث
        search_criteria['search_type'] = self.search_type.get()

        # بدء البحث في thread منفصل
        Thread(target=lambda: self.search_thread(search_criteria), daemon=True).start()

    def clear_results(self):
        """مسح النتائج"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح النتائج الحالية؟"):
            self.output_path.set("")
            self.status.set("تم مسح النتائج")

    def open_results_folder(self):
        """فتح مجلد النتائج"""
        output_file = self.output_path.get()
        if output_file and os.path.exists(output_file):
            folder_path = os.path.dirname(output_file)
            os.startfile(folder_path)
        else:
            messagebox.showinfo("معلومات", "لا يوجد ملف نتائج حتى الآن")

    def browse_input(self):
        """تصفح ملف الإدخال"""
        filetypes = [
            ("جميع الملفات المدعومة", "*.txt;*.csv;*.db"),
            ("ملفات نصية", "*.txt"),
            ("ملفات CSV", "*.csv"),
            ("قواعد بيانات SQLite", "*.db"),
            ("جميع الملفات", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="اختر ملف الإدخال",
            filetypes=filetypes
        )

        if filename:
            self.input_path.set(filename)

    def browse_output(self):
        """تصفح ملف الإخراج"""
        filename = filedialog.asksaveasfilename(
            title="حفظ النتائج باسم",
            defaultextension=".csv",
            filetypes=[("ملفات CSV", "*.csv"), ("جميع الملفات", "*.*")]
        )

        if filename:
            self.output_path.set(filename)

    def browse_fbids(self):
        """تصفح ملف المعرفات"""
        filename = filedialog.askopenfilename(
            title="اختر ملف المعرفات/الكلمات المفتاحية",
            filetypes=[("ملفات نصية", "*.txt"), ("جميع الملفات", "*.*")]
        )

        if filename:
            self.fb_ids_file.set(filename)

    def load_fbids(self):
        """تحميل معرفات Facebook من الملف"""
        if not self.fb_ids_file.get():
            return

        try:
            with open(self.fb_ids_file.get(), 'r', encoding='utf-8') as f:
                self.fb_ids = set()
                for line in f:
                    line = line.strip()
                    if line:
                        self.fb_ids.add(line.lower())

            self.status.set(f"تم تحميل {len(self.fb_ids)} معرف/كلمة مفتاحية")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة ملف المعرفات:\n{str(e)}")
            self.fb_ids = set()


                
    def remove_database(self):
        """إزالة قواعد البيانات المحددة"""
        selected = self.db_listbox.curselection()
        for index in reversed(selected):
            self.db_listbox.delete(index)
            
    def clear_databases(self):
        """مسح جميع قواعد البيانات"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع قواعد البيانات؟"):
            self.db_listbox.delete(0, tk.END)
            self.selected_dbs.clear()
            self.update_status_info()
        
    def get_database_list(self):
        """الحصول على قائمة قواعد البيانات المضافة"""
        return self.selected_dbs  # إرجاع المسارات الكاملة وليس أسماء الملفات فقط
        
    def read_database(self, filepath):
        """قراءة محتويات قاعدة البيانات حسب نوعها"""
        ext = os.path.splitext(filepath)[1].lower()
        
        if ext == '.csv':
            return self.read_csv_database(filepath)
        elif ext == '.txt':
            return self.read_txt_database(filepath)
        elif ext == '.db':
            return self.read_sqlite_database(filepath)
        else:
            raise ValueError(f"نوع الملف غير مدعوم: {ext}")
            
    def read_csv_database(self, filepath):
        """قراءة قاعدة بيانات CSV"""
        data = []
        with open(filepath, 'r', encoding='utf-8') as f:
            csv_reader = csv.reader(f)
            for row in csv_reader:
                data.append(row)
        return data
        
    def read_txt_database(self, filepath):
        """قراءة قاعدة بيانات TXT"""
        data = []
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                # تجربة تقسيم السطر بعدة فواصل محتملة
                for delimiter in [',', '\t', '|', ';']:
                    parts = line.strip().split(delimiter)
                    if len(parts) > 1:
                        data.append(parts)
                        break
                else:
                    # إذا لم يتم العثور على فاصل، نضيف السطر كاملاً
                    data.append([line.strip()])
        return data
        
    def read_sqlite_database(self, filepath):
        """قراءة قاعدة بيانات SQLite"""
        import sqlite3
        data = []
        try:
            conn = sqlite3.connect(filepath)
            cursor = conn.cursor()
            
            # الحصول على أسماء الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            # قراءة البيانات من كل جدول
            for table in tables:
                cursor.execute(f"SELECT * FROM {table[0]};")
                rows = cursor.fetchall()
                data.extend([list(row) for row in rows])
                
            conn.close()
            return data
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة قاعدة البيانات SQLite:\n{str(e)}")
            return []

    def browse_output(self):
        filename = filedialog.asksaveasfilename(
            title="حفظ ملف CSV",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.output_path.set(filename)

    def browse_input(self):
        """اختيار ملف الإدخال"""
        filename = filedialog.askopenfilename(
            title="اختيار ملف الإدخال",
            filetypes=[
                ("Text files", "*.txt"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.input_path.set(filename)
            self.status.set("تم اختيار ملف الإدخال")

    def browse_output(self):
        """اختيار ملف الإخراج"""
        filename = filedialog.asksaveasfilename(
            title="حفظ ملف الإخراج",
            defaultextension=".csv",
            filetypes=[
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.output_path.set(filename)
            self.status.set("تم تحديد ملف الإخراج")

    def browse_fbids(self):
        """اختيار ملف معرفات Facebook"""
        filename = filedialog.askopenfilename(
            title="اختيار ملف المعرفات",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.fb_ids = {line.strip() for line in f if line.strip()}
                self.fb_ids_file.set(filename)
                self.status.set(f"تم تحميل {len(self.fb_ids):,} معرف")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في قراءة الملف:\n{str(e)}")
                self.fb_ids = set()
                self.fb_ids_file.set("")

    def search_thread(self, search_criteria):
        """تنفيذ البحث في thread منفصل مع معالجة batch"""
        try:
            self.total_matches = 0
            self.processed_records = 0
            
            # تحقق من وجود ملف مخرجات
            output_file = self.output_path.get()
            if not output_file:
                messagebox.showwarning("تنبيه", "الرجاء تحديد ملف المخرجات")
                return

            # التحقق من قائمة قواعد البيانات المحددة
            if not self.selected_dbs:
                messagebox.showwarning("تنبيه", "الرجاء اختيار قاعدة بيانات واحدة على الأقل")
                return

            # تحديث معايير البحث
            self.current_criteria = search_criteria

            # تجميع معايير البحث
            criteria = {}

            # تجميع معايير البحث
            self.criteria = {}
            for field, var in self.search_values.items():
                value = var.get().strip()
                if value and value.lower() != 'all':
                    self.criteria[field] = value

            # حساب إجمالي عدد السجلات بكفاءة للملفات الضخمة
            self.status.set("🔍 جاري حساب حجم البيانات...")
            self.root.update_idletasks()

            self.total_records = 0
            total_dbs = len(self.selected_dbs)

            for i, db_path in enumerate(self.selected_dbs):
                try:
                    # تحديث التقدم أثناء الحساب
                    calc_progress = (i / total_dbs) * 15  # 15% للحساب (أقل لتوفير الوقت)
                    self.progress.set(calc_progress)
                    self.progress_percentage.set(f"{calc_progress:.1f}%")

                    file_size_mb = os.path.getsize(db_path) / (1024 * 1024)
                    self.status.set(f"📊 حساب الملف {i+1}/{total_dbs}: {os.path.basename(db_path)} ({file_size_mb:.1f} MB)")
                    self.root.update_idletasks()

                    if db_path.lower().endswith('.db'):
                        # SQLite - استخدام استعلام محسن
                        with sqlite3.connect(db_path) as conn:
                            cursor = conn.cursor()
                            cursor.execute("SELECT COUNT(*) FROM data")
                            self.total_records += cursor.fetchone()[0]
                    else:
                        # ملفات نصية - حساب سريع للملفات الكبيرة
                        if file_size_mb > 100:  # للملفات أكبر من 100 MB
                            # تقدير تقريبي بناءً على عينة
                            with open(db_path, 'r', encoding='utf-8') as f:
                                sample_lines = 0
                                sample_bytes = 0
                                for line in f:
                                    sample_lines += 1
                                    sample_bytes += len(line.encode('utf-8'))
                                    if sample_lines >= 10000:  # عينة من 10,000 سطر
                                        break

                                if sample_lines > 0:
                                    avg_line_size = sample_bytes / sample_lines
                                    estimated_lines = int(os.path.getsize(db_path) / avg_line_size)
                                    self.total_records += estimated_lines
                                    self.status.set(f"📊 تقدير {estimated_lines:,} سجل في {os.path.basename(db_path)}")
                        else:
                            # حساب دقيق للملفات الصغيرة
                            with open(db_path, 'r', encoding='utf-8') as f:
                                self.total_records += sum(1 for _ in f)

                except Exception as e:
                    messagebox.showerror("خطأ", f"خطأ في قراءة الملف {os.path.basename(db_path)}:\n{str(e)}")
                    return

            # تحديث المعلومات بعد الحساب
            self.status.set(f"✅ إجمالي {self.total_records:,} سجل في {total_dbs} ملف - بدء البحث...")
            self.progress.set(15)
            self.progress_percentage.set("15%")
            self.start_time = time.time()  # بدء حساب الوقت
            self.root.update_idletasks()

            # إعادة تعيين شريط التقدم
            self.progress.set(0)
            self.status.set("جاري البحث...")
            self.root.update()

            # بدء عملية البحث
            self.progress.set(0)
            self.status.set("جاري البحث...")
            
            with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                writer = csv.writer(outfile)

                # كتابة رؤوس الأعمدة
                headers = self.create_csv_headers()
                writer.writerow(headers)

                # معالجة كل قاعدة بيانات
                for db_path in self.selected_dbs:
                    self.process_database(db_path, writer)

            # إظهار نتيجة البحث النهائية
            self.progress.set(100)
            self.progress_percentage.set("100%")
            self.processed_count.set(f"{self.processed_records:,}")
            self.matches_count.set(f"{self.total_matches:,}")
            self.status.set(
                f"✅ اكتمل البحث بنجاح! تمت معالجة {self.processed_records:,} سجل "
                f"وتم العثور على {self.total_matches:,} نتيجة مطابقة"
            )

            # إظهار رسالة النجاح
            messagebox.showinfo(
                "🎉 اكتمل البحث",
                f"تم البحث بنجاح!\n\n"
                f"📊 السجلات المعالجة: {self.processed_records:,}\n"
                f"✅ النتائج المطابقة: {self.total_matches:,}\n"
                f"📁 تم حفظ النتائج في: {self.output_path.get()}"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث:\n{str(e)}")
            self.status.set("حدث خطأ أثناء البحث")
        finally:
            # إعادة تفعيل الأزرار
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.root.update()

    def process_database(self, db_path, writer):
        """معالجة قاعدة بيانات واحدة بأداء محسن للبيانات الضخمة"""
        try:
            if not hasattr(self, 'processed_records'):
                self.processed_records = 0

            # إعدادات محسنة للأداء
            chunk_size = self.chunk_size
            batch_size = self.batch_size

            # استخدام مخزن مؤقت أكبر للقراءة مع دعم ترميزات متعددة
            encodings_to_try = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']
            infile = None

            for encoding in encodings_to_try:
                try:
                    infile = open(db_path, 'r', encoding=encoding, buffering=chunk_size)
                    # اختبار قراءة السطر الأول
                    pos = infile.tell()
                    test_line = infile.readline()
                    infile.seek(pos)
                    print(f"✅ نجح فتح الملف بترميز: {encoding}")
                    break
                except (UnicodeDecodeError, UnicodeError):
                    if infile:
                        infile.close()
                    continue

            if not infile:
                print("❌ فشل في فتح الملف بأي ترميز")
                return

            with infile:
                batch = []
                line_count = 0

                # قراءة الملف بكفاءة أعلى
                for line in infile:
                    line_count += 1

                    # تجاهل الأسطر الفارغة
                    line = line.strip()
                    if not line:
                        continue

                    # تقسيم السطر بكفاءة مع معالجة CSV
                    try:
                        # استخدام csv.reader للتعامل مع الاقتباسات والفواصل بشكل صحيح
                        import csv
                        import io

                        # إنشاء StringIO للسطر الواحد
                        line_io = io.StringIO(line)
                        csv_reader = csv.reader(line_io, delimiter=',', quotechar='"')
                        row = next(csv_reader, None)

                        if row:
                            # تنظيف البيانات
                            cleaned_row = []
                            for cell in row:
                                # إزالة المسافات الزائدة
                                cell = cell.strip()
                                # تنظيف الرموز الغريبة
                                cell = self._clean_text(cell)
                                cleaned_row.append(cell)

                            batch.append(cleaned_row)

                    except Exception as e:
                        # في حالة فشل CSV، استخدم التقسيم العادي
                        try:
                            row = line.split(',')
                            cleaned_row = [self._clean_text(cell.strip(' "')) for cell in row]
                            batch.append(cleaned_row)
                        except Exception:
                            continue  # تجاهل الأسطر المعطوبة تماماً

                    # معالجة الدفعة عند امتلائها
                    if len(batch) >= batch_size:
                        self._process_batch(batch, writer, 0)
                        batch = []

                        # فحص إيقاف العملية
                        if hasattr(self, 'stop_event') and self.stop_event.is_set():
                            self.status.set("❌ تم إيقاف العملية")
                            return

                # معالجة آخر دفعة
                if batch:
                    self._process_batch(batch, writer, 0)

        except MemoryError:
            messagebox.showerror("خطأ", "نفدت الذاكرة! يرجى إغلاق البرامج الأخرى أو استخدام ملفات أصغر")
            self.status.set("❌ خطأ: نفدت الذاكرة")
        except Exception as e:
            error_msg = f"خطأ في معالجة الملف {os.path.basename(db_path)}: {str(e)}"
            self.status.set(error_msg)
            print(error_msg)  # للتشخيص

    def _process_batch(self, batch, writer, processed_records=0):
        """معالجة مجموعة من السجلات"""
        for row in batch:
            if self.match_criteria(row, self.current_criteria):
                # فلترة البيانات حسب الحقول المطلوبة
                filtered_row = self.filter_output_data(row)
                writer.writerow(filtered_row)
                self.total_matches += 1

            self.processed_records += 1
            processed_records += 1

            # تحديث أقل تكراراً للأداء الأفضل مع البيانات الضخمة
            if self.processed_records % self.update_frequency == 0:
                progress = (self.processed_records / self.total_records) * 100
                self.progress.set(progress)
                self.progress_percentage.set(f"{progress:.1f}%")
                self.processed_count.set(f"{self.processed_records:,}")
                self.matches_count.set(f"{self.total_matches:,}")

                # حساب السرعة
                import time
                if not hasattr(self, 'start_time'):
                    self.start_time = time.time()

                elapsed_time = time.time() - self.start_time
                if elapsed_time > 0:
                    records_per_second = self.processed_records / elapsed_time
                    eta_seconds = (self.total_records - self.processed_records) / records_per_second if records_per_second > 0 else 0
                    eta_minutes = eta_seconds / 60

                    self.status.set(
                        f"🔍 معالجة {self.processed_records:,} من {self.total_records:,} سجل "
                        f"({progress:.1f}%) - السرعة: {records_per_second:,.0f} سجل/ثانية - "
                        f"الوقت المتبقي: {eta_minutes:.1f} دقيقة"
                    )
                else:
                    self.status.set(
                        f"🔍 معالجة {self.processed_records:,} من {self.total_records:,} سجل ({progress:.1f}%)"
                    )

                # تحديث الواجهة بشكل غير متزامن
                self.root.update_idletasks()  # أسرع من update()

        return processed_records

    def create_csv_headers(self):
        """إنشاء رؤوس الأعمدة لملف CSV بناءً على الواجهة الجديدة"""
        headers = []

        # رؤوس الأعمدة للحقول الجديدة
        field_headers = {
            'facebook_id': 'Facebook ID',
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'E-mail',
            'phone': 'Phone',
            'birthday': 'Birthday',
            'birthday_year': 'Birthday Year',
            'locale': 'Locale',
            'hometown': 'Hometown',
            'work': 'Work',
            'country': 'Country',
            'education': 'Education',
            'relationship': 'Relationship',
            'religion': 'Religion',
            'about_me': 'About Me',
            'gender': 'Gender',
            'location': 'Location'
        }

        # إضافة رؤوس الأعمدة حسب الحقول المفعلة
        for field_type, var in self.output_fields.items():
            if var.get():
                headers.append(field_headers.get(field_type, field_type))

        return headers if headers else ['Data']

    def search_records(self):
        """بدء عملية البحث في thread منفصل"""
        # تحقق من وجود قواعد بيانات مختارة
        if not self.get_database_list():
            messagebox.showwarning("تنبيه", "الرجاء اختيار قاعدة بيانات واحدة على الأقل")
            return

        if not self.output_path.get():
            messagebox.showwarning("تنبيه", "الرجاء تحديد ملف المخرجات")
            return

        # جمع معايير البحث
        search_criteria = {}
        for field, var in self.search_values.items():
            value = var.get().strip()
            if value and value.lower() != 'all':
                search_criteria[field] = value
            
        # بدء البحث في thread منفصل
        Thread(target=lambda: self.search_thread(search_criteria), daemon=True).start()

    def match_criteria(self, current_row, current_criteria=None):
        """التحقق من تطابق السجل مع معايير البحث"""
        # التحقق من معرفات Facebook - البحث المرن في كامل الصف
        # لأن الملف المرفوع غير معرف القيمة (قد يكون في لينك البروفيل أو معرف الـ ID أو الإيميل)
        if hasattr(self, 'fb_ids') and self.fb_ids:
            found_match = False
            for search_term in self.fb_ids:
                if not search_term:
                    continue

                search_term_lower = search_term.lower().strip()

                # البحث في كامل الصف مع تنظيف البيانات
                for cell in current_row:
                    cell_str = str(cell).lower().strip()
                    # تنظيف النص للبحث
                    cell_clean = self._clean_text(cell_str).lower()

                    # البحث المرن - يدعم:
                    # 1. البحث المباشر
                    # 2. البحث في الروابط (facebook.com/profile.php?id=123)
                    # 3. البحث في أرقام الهواتف (+201002258912)
                    # 4. البحث في المعرفات (100007319070330)
                    if (search_term_lower in cell_str or
                        search_term_lower in cell_clean or
                        cell_str in search_term_lower or
                        cell_clean in search_term_lower or
                        self._flexible_search_match(search_term_lower, cell_str) or
                        self._flexible_search_match(search_term_lower, cell_clean) or
                        self._phone_number_match(search_term_lower, cell_str)):
                        found_match = True
                        break

                if found_match:
                    break

            if not found_match:
                return False

        # التحقق من معايير البحث المخصصة
        if current_criteria:
            for field, value in current_criteria.items():
                if not value:
                    continue

                # البحث العام في جميع الحقول
                if field == 'general_search':
                    found_match = False
                    for cell in current_row:
                        if str(value).lower() in str(cell).lower():
                            found_match = True
                            break
                    if not found_match:
                        return False

                # البحث في حقول محددة (الفلاتر)
                elif field != 'search_type':
                    found_match = False
                    field_index = self._get_field_index(field)

                    if field_index is not None and field_index < len(current_row):
                        cell_value = str(current_row[field_index]).lower().strip()
                        search_value = str(value).lower().strip()

                        # دعم البحث المرن والجزئي
                        if (search_value in cell_value or
                            cell_value in search_value or
                            self._flexible_search_match(search_value, cell_value)):
                            found_match = True
                    else:
                        # إذا لم يوجد الحقل، ابحث في كامل الصف
                        for cell in current_row:
                            cell_str = str(cell).lower().strip()
                            if (search_value in cell_str or
                                self._flexible_search_match(search_value, cell_str)):
                                found_match = True
                                break

                    # إذا لم نجد تطابق لهذا الفلتر، ارفض السجل
                    if not found_match:
                        return False

        return True

    def _get_field_index(self, field_name):
        """تحديد فهرس الحقل في الصف"""
        field_mapping = {
            'facebook_id': 0,
            'first_name': 1,
            'last_name': 2,
            'email': 3,
            'phone': 4,
            'birthday': 5,
            'birthday_year': 6,
            'locale': 7,
            'hometown': 8,
            'work': 9,
            'country': 10,
            'education': 11,
            'relationship': 12,
            'religion': 13,
            'about_me': 14,
            'gender': 15,
            'location': 16
        }
        return field_mapping.get(field_name)

    def _flexible_search_match(self, search_term, cell_value):
        """البحث المرن للتعامل مع أنواع مختلفة من البيانات"""
        try:
            # إزالة المسافات والرموز الخاصة
            search_clean = ''.join(c for c in search_term if c.isalnum())
            cell_clean = ''.join(c for c in cell_value if c.isalnum())

            # البحث في الأرقام فقط (للهواتف والمعرفات)
            search_digits = ''.join(c for c in search_term if c.isdigit())
            cell_digits = ''.join(c for c in cell_value if c.isdigit())

            # البحث في النصوص فقط (للأسماء)
            search_alpha = ''.join(c for c in search_term if c.isalpha())
            cell_alpha = ''.join(c for c in cell_value if c.isalpha())

            # تطابق مرن
            return (
                (search_clean and search_clean in cell_clean) or
                (cell_clean and cell_clean in search_clean) or
                (search_digits and len(search_digits) > 3 and search_digits in cell_digits) or
                (cell_digits and len(cell_digits) > 3 and cell_digits in search_digits) or
                (search_alpha and len(search_alpha) > 2 and search_alpha in cell_alpha) or
                (cell_alpha and len(cell_alpha) > 2 and cell_alpha in search_alpha)
            )
        except:
            return False

    def _clean_text(self, text):
        """تنظيف النصوص من الرموز الغريبة والترميز الخاطئ"""
        if not text:
            return ""

        try:
            # إزالة الرموز الغريبة الشائعة
            replacements = {
                'ï±‍': '',
                'غ¦': '',
                'ط¹': 'ع',
                'ظ…': 'م',
                'ط±': 'ر',
                'ظˆ': 'و',
                'ط£': 'أ',
                'ط¨': 'ب',
                'ظˆ': 'و',
                'ط¹': 'ع',
                'ظ…': 'م',
                'ط±': 'ر',
                'ظƒ': 'ك',
                'ظ„': 'ل',
                'ط§': 'ا',
                'ظ…': 'م',
                'ظپ': 'ف',
                'ظ‰': 'ى',
                'ط­': 'ح',
                'ط¨': 'ب',
                'ظ„': 'ل',
                'ط§': 'ا',
                'ظ„': 'ل',
                'ظ‡': 'ه',
                'ط§': 'ا',
                'ظ„': 'ل',
                'ط§': 'ا',
                'ط§': 'ا',
                'ظ„': 'ل',
                'ظ„': 'ل',
                'ظ‡': 'ه',
                'ظ…': 'م',
                'ط­': 'ح',
                'ظ…': 'م',
                'ط¯': 'د',
                'ط±': 'ر',
                'ط³': 'س',
                'ظˆ': 'و',
                'ظ„': 'ل',
                'ط§': 'ا',
                'ظ„': 'ل',
                'ظ„': 'ل',
                'ظ‡': 'ه'
            }

            # تطبيق الاستبدالات
            cleaned = text
            for old, new in replacements.items():
                cleaned = cleaned.replace(old, new)

            # إزالة الرموز الغريبة المتبقية
            import re
            # إزالة الرموز غير المطبوعة
            cleaned = re.sub(r'[^\x20-\x7E\u0600-\u06FF\u0750-\u077F]', '', cleaned)

            # إزالة المسافات المتعددة
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()

            return cleaned

        except Exception:
            # في حالة فشل التنظيف، إرجاع النص الأصلي
            return str(text).strip()

    def _phone_number_match(self, search_term, cell_value):
        """البحث المتخصص في أرقام الهواتف المصرية"""
        try:
            # استخراج الأرقام فقط
            search_digits = ''.join(c for c in search_term if c.isdigit())
            cell_digits = ''.join(c for c in cell_value if c.isdigit())

            if len(search_digits) < 8:  # رقم قصير جداً
                return False

            # أنماط أرقام الهواتف المصرية
            egyptian_patterns = [
                # أرقام الموبايل المصرية
                r'(\+?20)?(10|11|12|15)\d{8}',
                # أرقام الأرضي المصرية
                r'(\+?20)?[2-9]\d{7,8}',
                # أرقام دولية
                r'\+?\d{10,15}'
            ]

            import re

            # فحص الأنماط
            for pattern in egyptian_patterns:
                if re.search(pattern, search_digits) and re.search(pattern, cell_digits):
                    # مقارنة الأرقام الأساسية
                    if search_digits in cell_digits or cell_digits in search_digits:
                        return True

                    # مقارنة بدون رمز الدولة
                    search_no_country = search_digits.lstrip('20')
                    cell_no_country = cell_digits.lstrip('20')

                    if search_no_country in cell_no_country or cell_no_country in search_no_country:
                        return True

            return False

        except Exception:
            return False

    def _get_field_indices_for_search(self, field_type):
        """تحديد فهارس الحقول المناسبة لكل نوع بحث"""
        # هذه الفهارس تعتمد على بنية البيانات المتوقعة
        # يمكن تعديلها حسب بنية ملفات البيانات الفعلية
        field_mapping = {
            'facebook_id': [0, 1, 2],  # الحقول المحتملة لـ Facebook ID
            'name': [3, 4, 5, 6],      # الحقول المحتملة للأسماء
            'email': [7, 8, 9],        # الحقول المحتملة للإيميل
            'phone': [10, 11, 12],     # الحقول المحتملة للهاتف
            'location': [13, 14, 15],  # الحقول المحتملة للموقع
            'age': [16, 17],           # الحقول المحتملة للعمر
            'gender': [18, 19],        # الحقول المحتملة للجنس
            'relationship': [20, 21],  # الحقول المحتملة للحالة الاجتماعية
            'work': [22, 23, 24],      # الحقول المحتملة للعمل
            'education': [25, 26, 27], # الحقول المحتملة للتعليم
            'interests': [28, 29, 30]  # الحقول المحتملة للاهتمامات
        }

        return field_mapping.get(field_type, list(range(50)))  # البحث في جميع الحقول إذا لم يتم العثور على تطابق

    def _flexible_match(self, cell_value, search_value):
        """بحث مرن يدعم أنماط مختلفة"""
        # البحث الأساسي
        if search_value in cell_value:
            return True

        # البحث بالكلمات المنفصلة
        search_words = search_value.split()
        if len(search_words) > 1:
            for word in search_words:
                if word in cell_value:
                    return True

        # البحث بالأرقام (للهاتف والعمر)
        if search_value.isdigit():
            # إزالة الرموز والبحث بالأرقام فقط
            cell_digits = ''.join(filter(str.isdigit, cell_value))
            if search_value in cell_digits:
                return True

        # البحث بالنطاقات (للإيميل)
        if '@' in search_value or '.' in search_value:
            if search_value in cell_value:
                return True

        # البحث بالمرادفات الشائعة
        synonyms = {
            'متزوج': ['married', 'زواج', 'متزوجة'],
            'أعزب': ['single', 'عزباء', 'غير متزوج'],
            'مطلق': ['divorced', 'مطلقة'],
            'ذكر': ['male', 'رجل', 'man'],
            'أنثى': ['female', 'امرأة', 'woman'],
            'القاهرة': ['cairo', 'قاهرة'],
            'الإسكندرية': ['alexandria', 'اسكندرية'],
            'الجيزة': ['giza', 'جيزة']
        }

        for arabic, english_list in synonyms.items():
            if search_value == arabic.lower():
                for eng in english_list:
                    if eng in cell_value:
                        return True
            elif search_value in english_list:
                if arabic in cell_value:
                    return True

        return False

    def filter_output_data(self, row):
        """فلترة البيانات حسب الحقول المطلوبة الجديدة"""
        if not row:
            return row

        # إنشاء صف جديد بالبيانات المطلوبة فقط
        filtered_row = []

        # تحديد الحقول المطلوبة بناءً على الواجهة الجديدة
        field_mapping = {
            'facebook_id': [0],      # Facebook ID
            'first_name': [1],       # First Name
            'last_name': [2],        # Last Name
            'email': [3],            # Email
            'phone': [4],            # Phone
            'birthday': [5],         # Birthday
            'birthday_year': [6],    # Birthday Year
            'locale': [7],           # Locale
            'hometown': [8],         # Hometown
            'work': [9],             # Work
            'country': [10],         # Country
            'education': [11],       # Education
            'relationship': [12],    # Relationship
            'religion': [13],        # Religion
            'about_me': [14],        # About Me
            'gender': [15],          # Gender
            'location': [16]         # Location
        }

        # إضافة البيانات المطلوبة
        for field_type, var in self.output_fields.items():
            if var.get():  # إذا كان الحقل مفعل
                indices = field_mapping.get(field_type, [])
                field_data = []

                for index in indices:
                    if index < len(row) and row[index]:
                        field_data.append(str(row[index]).strip())

                # دمج البيانات من نفس النوع
                combined_data = ' | '.join(filter(None, field_data))
                filtered_row.append(combined_data if combined_data else '')

        return filtered_row if filtered_row else row

    def process_batch(self, batch, writer):
        """معالجة دفعة من السجلات"""
        matches = 0
        for row in batch:
            if self.match_criteria(row):
                writer.writerow(row)
                matches += 1
        return matches

    def start_processing(self):
        """بدء عملية المعالجة"""
        if self.processing:
            return
            
        if not self.input_path.get() or not self.output_path.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملفي الإدخال والإخراج")
            return
            
        self.processing = True
        self.stop_event.clear()
        Thread(target=self.process_file, daemon=True).start()

    def stop_processing(self):
        """إيقاف عملية المعالجة"""
        if hasattr(self, 'processing') and self.processing:
            self.stop_event.set()
            self.status.set("⏹️ جاري إيقاف العملية...")
            self.progress_percentage.set("متوقف")

        # إعادة تفعيل الأزرار
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')

        # تحديث الحالة
        self.status.set("❌ تم إيقاف العملية بواسطة المستخدم")
        self.root.update()

    def update_progress_detailed(self, current_db_index, total_dbs, current_file_progress):
        """تحديث شريط التقدم بتفاصيل أكثر"""
        try:
            # حساب التقدم الإجمالي
            # 20% للحساب الأولي + 80% للمعالجة
            base_progress = 20
            processing_progress = (current_db_index / total_dbs) * 80
            file_progress = (current_file_progress / 100) * (80 / total_dbs)

            total_progress = base_progress + processing_progress + file_progress
            total_progress = min(100, max(0, total_progress))

            self.progress.set(total_progress)
            self.progress_percentage.set(f"{total_progress:.1f}%")
            self.processed_count.set(f"{self.processed_records:,}")
            self.matches_count.set(f"{self.total_matches:,}")

            # تحديث النص التفصيلي
            if current_db_index < total_dbs:
                self.status.set(
                    f"🔍 معالجة الملف {current_db_index + 1} من {total_dbs} "
                    f"({current_file_progress:.1f}% من الملف الحالي)"
                )

            self.root.update()

        except Exception as e:
            print(f"خطأ في تحديث شريط التقدم: {e}")

    def reset_progress(self):
        """إعادة تعيين شريط التقدم"""
        self.progress.set(0)
        self.progress_percentage.set("0%")
        self.processed_count.set("0")
        self.matches_count.set("0")
        self.status.set("جاهز للبدء...")
        self.root.update()

    def optimize_for_large_data(self):
        """تحسينات خاصة للبيانات الضخمة"""
        try:
            import gc

            # تنظيف الذاكرة
            gc.collect()

            # تحذير المستخدم للملفات الكبيرة جداً
            total_size_gb = 0
            for db_path in self.selected_dbs:
                if os.path.exists(db_path):
                    total_size_gb += os.path.getsize(db_path) / (1024**3)

            if total_size_gb > 5:  # أكبر من 5 GB
                response = messagebox.askyesno(
                    "تحذير - ملفات كبيرة",
                    f"حجم البيانات الإجمالي: {total_size_gb:.1f} GB\n\n"
                    "هذا قد يستغرق وقتاً طويلاً ويستهلك ذاكرة كبيرة.\n"
                    "هل تريد المتابعة؟\n\n"
                    "نصائح للأداء الأفضل:\n"
                    "• أغلق البرامج الأخرى\n"
                    "• تأكد من وجود مساحة كافية على القرص\n"
                    "• استخدم البحث السريع"
                )
                if not response:
                    return False

            return True

        except Exception as e:
            print(f"تحذير: خطأ في التحسين: {e}")
            return True  # متابعة حتى لو فشل التحسين

    def browse_ids_file(self):
        """فتح نافذة اختيار ملف الـ Facebook IDs"""
        filename = filedialog.askopenfilename(
            title="اختر ملف Facebook IDs",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                # قراءة الـ IDs من الملف
                with open(filename, 'r', encoding='utf-8') as f:
                    self.fb_ids_set = set(line.strip() for line in f if line.strip())
                self.fb_ids_input.set(filename)
                messagebox.showinfo("نجاح", f"تم تحميل {len(self.fb_ids_set)} معرف من الملف")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء قراءة الملف:\n{str(e)}")
                self.fb_ids_set = set()
                self.fb_ids_input.set("")

    def get_line_count(self, filename):
        self.status.set("جاري حساب عدد الأسطر...")
        count = 0
        with open(filename, 'rb') as f:
            for _ in f:
                count += 1
        return count

    def process_in_batches(self, input_file, output_file, batch_size=10000):
        """معالجة الملف في مجموعات لتحسين الأداء والذاكرة"""
        total_processed = 0
        batch = []
        
        try:
            with open(input_file, 'r', encoding='utf-8') as infile, \
                 open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                writer = csv.writer(outfile)
                
                for line in infile:
                    batch.append(line.strip().split(self.delimiter.get()))
                    
                    if len(batch) >= batch_size:
                        self._process_batch(batch, writer)
                        total_processed += len(batch)
                        batch = []
                        
                        # تحديث التقدم
                        self.status.set(f"تمت معالجة {total_processed:,} سجل")
                        self.root.update()
                
                # معالجة آخر مجموعة
                if batch:
                    self._process_batch(batch, writer)
                    total_processed += len(batch)
                    
        except Exception as e:
            raise Exception(f"خطأ في معالجة الملف: {str(e)}")
            
        return total_processed

    def should_include_line(self, row):
        """التحقق مما إذا كان يجب تضمين السطر في المخرجات"""
        # التحقق من معايير التصفية
        for option, is_enabled in self.filter_options.items():
            if not is_enabled.get():
                continue

            filter_value = self.filter_values[option].get().strip()
            if not filter_value:
                continue

            values = [v.strip().lower() for v in filter_value.split(',')]
            found_match = False

            for cell in row:
                cell_str = str(cell).lower()
                if any(value in cell_str for value in values):
                    found_match = True
                    break

            if not found_match:
                return False

        # التحقق من الكلمات المفتاحية
        if self.use_keywords.get() and self.keywords.get().strip():
            keywords = [k.strip().lower() for k in self.keywords.get().split(',')]
            found_keyword = False
            for cell in row:
                cell_str = str(cell).lower()
                if any(keyword in cell_str for keyword in keywords):
                    found_keyword = True
                    break
            if not found_keyword:
                return False

        # التحقق من المنطقة
        if self.use_region.get() and self.region.get().strip():
            region = self.region.get().strip().lower()
            found_region = False
            for cell in row:
                if region in str(cell).lower():
                    found_region = True
                    break
            if not found_region:
                return False

        # التحقق من معرفات Facebook
        if self.use_fbids.get() and self.fb_ids:
            found_id = False
            for cell in row:
                cell_str = str(cell).lower()
                if any(fb_id.lower() in cell_str for fb_id in self.fb_ids):
                    found_id = True
                    break
            if not found_id:
                return False

        return True

    def _process_batch(self, batch, writer):
        """معالجة مجموعة من السجلات"""
        for row in batch:
            if self.should_include_line(row):
                writer.writerow(row)

    def convert_file(self):
        try:
            input_file = self.input_path.get()
            output_file = self.output_path.get()
            batch_size = 10000  # حجم المجموعة للمعالجة

            if not input_file or not output_file:
                messagebox.showerror("خطأ", "يرجى اختيار ملفي الإدخال والإخراج")
                return

            total_lines = self.get_line_count(input_file)
            file_size = os.path.getsize(input_file)
            
            self.status.set(f"حجم الملف: {file_size / (1024*1024*1024):.2f} GB - عدد الأسطر: {total_lines:,}")
            self.root.update()

            # إنشاء مجلد للمخرجات
            output_dir = os.path.splitext(output_file)[0] + "_parts"
            os.makedirs(output_dir, exist_ok=True)

            processed_lines = 0
            current_file_number = 1
            current_file_lines = 0
            current_csv_file = None
            current_csv_writer = None

            with open(input_file, 'r', encoding='utf-8', buffering=self.chunk_size) as txt_file:
                while True:
                    # فتح ملف CSV جديد عند الحاجة
                    if current_csv_file is None:
                        current_file_path = os.path.join(output_dir, f'part_{current_file_number:03d}.csv')
                        current_csv_file = open(current_file_path, 'w', newline='', encoding='utf-8')
                        current_csv_writer = csv.writer(current_csv_file)
                        self.status.set(f"جاري الكتابة في الملف رقم {current_file_number}")
                    line = txt_file.readline()
                    if not line:  # نهاية الملف
                        break
                    
                    # معالجة السطر
                    line = line.strip()
                    
                    # تحليل السطر كـ CSV
                    try:
                        import csv as csv_parser
                        fields = next(csv_parser.reader([line]))
                        
                        # تطبيق الفلاتر
                        if not self.should_include_line(fields):
                            continue
                        
                        # كتابة السطر المقبول
                        current_csv_writer.writerow(fields)
                        processed_lines += 1
                        current_file_lines += 1
                    except Exception as e:
                        print(f"خطأ في معالجة السطر: {e}")
                        continue

                    # تحديث شريط التقدم وحالة المعالجة
                    progress = (processed_lines / total_lines) * 100
                    self.progress.set(progress)

                    if processed_lines % 1000 == 0:
                        self.status.set(f"تم معالجة {processed_lines:,} من {total_lines:,} سطر - ملف {current_file_number}")
                        self.root.update()

                    # إغلاق الملف الحالي وفتح ملف جديد عند الوصول للحد
                    if current_file_lines >= self.lines_per_file:
                        current_csv_file.close()
                        current_csv_file = None
                        current_file_number += 1
                        current_file_lines = 0

            # إغلاق آخر ملف
            if current_csv_file:
                current_csv_file.close()

            self.status.set(f"اكتمل التحويل بنجاح! تم إنشاء {current_file_number} ملف في المجلد {output_dir}")
            messagebox.showinfo("نجاح", f"تم تحويل الملف بنجاح وتقسيمه إلى {current_file_number} ملف!")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التحويل:\n{str(e)}")
            self.status.set("حدث خطأ أثناء التحويل")

    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        # التحقق من اختيار ملف الإدخال
        if not self.get_database_list():
            messagebox.showwarning("تنبيه", "الرجاء اختيار قاعدة بيانات واحدة على الأقل")
            return False

        # التحقق من تحديد ملف الإخراج
        if not self.output_path.get():
            messagebox.showwarning("تنبيه", "الرجاء تحديد ملف المخرجات")
            return False

        # التحقق من تحديد حقل واحد على الأقل للإخراج
        if not any(var.get() for var in self.output_fields.values()):
            messagebox.showwarning("تنبيه", "الرجاء تحديد حقل واحد على الأقل للإخراج")
            return False

        return True

    def start_conversion(self):
        if not self.validate_inputs():
            return
        
        # تشغيل التحويل في thread منفصل
        Thread(target=self.convert_file, daemon=True).start()

    def convert_file(self):
        try:
            input_file = self.input_path.get()
            output_file = self.output_path.get()
            
            # حساب إجمالي عدد الأسطر
            self.status.set("جاري حساب عدد الأسطر...")
            total_lines = sum(1 for _ in open(input_file, 'r', encoding='utf-8'))
            
            # تحميل Facebook IDs إذا تم تحديد الملف
            if self.fb_ids_file.get():
                self.load_fbids()
            
            processed_lines = 0
            matches_found = 0
            
            # فتح ملف الإخراج
            with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                writer = csv.writer(outfile)
                
                # قراءة وتحويل البيانات
                with open(input_file, 'r', encoding='utf-8') as infile:
                    for line in infile:
                        # معالجة السطر
                        fields = line.strip().split(self.delimiter.get())
                        
                        # تطبيق الفلتر إذا كان مطلوباً
                        if self.fb_ids and not self.should_include_line(fields):
                            processed_lines += 1
                            continue
                            
                        # كتابة السطر في ملف الإخراج
                        writer.writerow(fields)
                        matches_found += 1
                        processed_lines += 1
                        
                        # تحديث شريط التقدم
                        progress = (processed_lines / total_lines) * 100
                        self.progress.set(progress)
                        
                        # تحديث النص كل 1000 سطر
                        if processed_lines % 1000 == 0:
                            self.status.set(
                                f"جاري المعالجة... {processed_lines:,} من {total_lines:,} "
                                f"({progress:.1f}%) - تم العثور على {matches_found:,} نتيجة"
                            )
                            self.root.update()
            
            # رسالة اكتمال العملية
            self.status.set(f"اكتملت العملية! تم العثور على {matches_found:,} نتيجة")
            messagebox.showinfo("نجاح", f"تم معالجة {processed_lines:,} سطر وتم العثور على {matches_found:,} نتيجة")
            
        except Exception as e:
            self.status.set("حدث خطأ أثناء المعالجة")
            messagebox.showerror("خطأ", str(e))
        finally:
            self.progress.set(0)

if __name__ == "__main__":
    try:
        root = tk.Tk()
        root.title("معالج بيانات Facebook")
        
        # تطبيق نمط عربي من اليمين لليسار
        try:
            root.tk.call('tk', 'scaling', 1.3)
        except:
            pass
            
        app = ConversionApp(root)
        root.mainloop()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {str(e)}")

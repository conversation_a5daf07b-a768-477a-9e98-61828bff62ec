# 🧹 CSV Cleaner & Formatter Tool
## أداة تنظيف وتنسيق ملفات CSV للبيانات الاجتماعية

### 📋 الوصف
أداة Python متخصصة لتنظيف وتحسين ملفات CSV الكبيرة، خاصة البيانات الاجتماعية من Facebook وغيرها. تحل مشكلة البيانات غير المنظمة وتوحد تنسيق جميع الملفات لتسهيل عمليات البحث.

### ✨ المميزات الرئيسية

#### 🔧 تنظيف البيانات
- **إزالة الرموز الغريبة** والأحرف التحكمية
- **تصحيح مشاكل الترميز** الشائعة (UTF-8, Latin1, etc.)
- **تنظيف علامات الاقتباس** المكررة والمسافات الزائدة
- **إزالة السجلات المكررة** بذكاء

#### 📊 توحيد التنسيق
- **رؤوس أعمدة موحدة** لجميع الملفات
- **تحويل أسماء الحقول** إلى تنسيق قياسي
- **ترتيب الأعمدة** حسب الأولوية
- **تنسيق البيانات** بشكل متسق

#### ⚡ معالجة البيانات الضخمة
- **تقسيم الملفات الكبيرة** (أكبر من مليون سجل)
- **معالجة batch** للذاكرة المحدودة
- **كشف تلقائي للترميز** والفواصل
- **شريط تقدم مفصل** مع إحصائيات

### 🎯 رؤوس الأعمدة الموحدة

```
facebook_id      - معرف Facebook
first_name       - الاسم الأول
last_name        - اسم العائلة
email           - البريد الإلكتروني
phone           - رقم الهاتف
gender          - الجنس
birthday        - تاريخ الميلاد
birthday_year   - سنة الميلاد
location        - الموقع الحالي
hometown        - المدينة الأصلية
country         - البلد
locale          - اللغة/المنطقة
username        - اسم المستخدم
profile_url     - رابط الملف الشخصي
education       - التعليم
work            - العمل
religion        - الديانة
relationship    - الحالة الاجتماعية
about_me        - نبذة شخصية
interests       - الاهتمامات
```

### 🚀 كيفية الاستخدام

#### 1. تشغيل الأداة
```bash
python csv_cleaner_tool.py
```

#### 2. اختيار المجلدات
- **مجلد الإدخال**: اختر المجلد الذي يحتوي على ملفات CSV المراد تنظيفها
- **مجلد الإخراج**: اختر المجلد لحفظ الملفات المنظفة

#### 3. تحديد خيارات التنظيف
- ✅ **توحيد رؤوس الأعمدة**: تحويل جميع أسماء الأعمدة للتنسيق الموحد
- ✅ **تنظيف البيانات**: إزالة الرموز الغريبة والمسافات الزائدة
- ✅ **إصلاح الترميز**: تصحيح مشاكل الترميز الشائعة
- ✅ **إزالة المكررات**: حذف السجلات المتطابقة
- ✅ **تقسيم الملفات الكبيرة**: تقسيم الملفات أكبر من مليون سجل

#### 4. بدء التنظيف
- اضغط **"🚀 بدء التنظيف"**
- راقب التقدم والإحصائيات
- انتظر حتى اكتمال العملية

### 📈 الإحصائيات المعروضة
- **الملفات المعالجة**: عدد الملفات المكتملة / الإجمالي
- **السجلات المعالجة**: إجمالي عدد السجلات المعالجة
- **المكررات المحذوفة**: عدد السجلات المكررة المحذوفة
- **الأخطاء المصححة**: عدد الأخطاء التي تم إصلاحها

### 🔄 مثال على التحويل

#### قبل التنظيف:
```csv
ID,Name,Mail,Tel,Sex,Location
"100001234567890","أحمد محمد","<EMAIL>","01012345678","ذكر","القاهرة، مصر"
"100001234567891","فاطمة علي","<EMAIL>","01098765432","أنثى","الإسكندرية"
```

#### بعد التنظيف:
```csv
facebook_id,first_name,last_name,email,phone,gender,location
100001234567890,أحمد,محمد,<EMAIL>,01012345678,ذكر,القاهرة، مصر
100001234567891,فاطمة,علي,<EMAIL>,01098765432,أنثى,الإسكندرية
```

### ⚙️ الإعدادات المتقدمة

#### تقسيم الملفات الكبيرة
- **الحد الافتراضي**: 1,000,000 سجل لكل ملف
- **يمكن تعديله** حسب قدرة النظام
- **تسمية تلقائية**: `filename_part_001.csv`, `filename_part_002.csv`

#### كشف الترميز التلقائي
الأداة تجرب الترميزات التالية بالترتيب:
1. UTF-8
2. UTF-8 with BOM
3. Latin1
4. CP1252
5. ISO-8859-1

### 🛠️ متطلبات النظام
- **Python 3.6+**
- **مكتبات مطلوبة**: tkinter (مدمجة مع Python)
- **ذاكرة**: 4GB+ للملفات الكبيرة
- **مساحة تخزين**: ضعف حجم الملفات الأصلية

### 📁 هيكل الملفات الناتجة
```
output_folder/
├── file1_cleaned.csv
├── file2_part_001_cleaned.csv
├── file2_part_002_cleaned.csv
└── file3_cleaned.csv
```

### 🔍 نصائح للاستخدام الأمثل

#### للملفات الضخمة (أكبر من 5GB):
1. **فعل تقسيم الملفات** (500,000 سجل لكل ملف)
2. **أغلق البرامج الأخرى** لتوفير الذاكرة
3. **استخدم SSD** لسرعة أكبر في القراءة/الكتابة

#### لتحسين الأداء:
1. **ضع الملفات على نفس القرص** (الإدخال والإخراج)
2. **تأكد من وجود مساحة كافية** (ضعف حجم البيانات)
3. **راقب استخدام الذاكرة** أثناء المعالجة

### ❗ تحذيرات مهمة
- **احتفظ بنسخة احتياطية** من الملفات الأصلية
- **تأكد من مساحة التخزين** الكافية
- **لا تغلق البرنامج** أثناء المعالجة
- **استخدم أزرار الإيقاف** إذا احتجت لإيقاف العملية

### 🆘 حل المشاكل الشائعة

#### "نفدت الذاكرة":
- قلل عدد السجلات لكل ملف إلى 500,000
- أغلق البرامج الأخرى
- أعد تشغيل الكمبيوتر

#### "خطأ في الترميز":
- الأداة تتعامل مع هذا تلقائياً
- إذا استمر الخطأ، تحقق من سلامة الملف الأصلي

#### "الملفات لا تفتح":
- تأكد من أن الملفات بصيغة CSV صحيحة
- جرب فتح الملف في Excel أو محرر نصوص

### 📞 الدعم والمساعدة
إذا واجهت أي مشاكل أو احتجت مساعدة، يمكنك:
1. التحقق من رسائل الخطأ في الواجهة
2. مراجعة هذا الدليل
3. التأكد من متطلبات النظام

---
**تم تطوير هذه الأداة لتحسين كفاءة البحث في البيانات الضخمة وتوفير الوقت والجهد في تنظيف البيانات.**

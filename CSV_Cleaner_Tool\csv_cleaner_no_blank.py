#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - نسخة بدون أعمدة blank
أداة تنظيف ملفات CSV مع حذف أعمدة blank من الرؤوس والبيانات
"""

import os
import csv
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class NoBlankCSVCleaner:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - No Blank Columns")
        self.root.geometry("900x600")
        self.root.configure(pady=10, padx=10)
        
        # النسق الثابت بدون أعمدة blank (19 عمود)
        self.standard_headers = [
            'facebook_id',      # 0 - معرف Facebook
            'email',            # 1 - البريد الإلكتروني
            'phone',            # 2 - رقم الهاتف
            'religion',         # 3 - الديانة
            'birthday_year',    # 4 - سنة الميلاد
            'first_name',       # 5 - الاسم الأول
            'last_name',        # 6 - اسم العائلة
            'gender',           # 7 - الجنس
            'link',             # 8 - الرابط
            'username',         # 9 - اسم المستخدم
            'fullname',         # 10 - الاسم الكامل
            'beo',              # 11 - beo
            'company',          # 12 - الشركة
            'title',            # 13 - المسمى الوظيفي
            'hometown',         # 14 - المدينة الأصلية
            'country',          # 15 - البلد
            'education',        # 16 - التعليم
            'user',             # 17 - المستخدم
            'status'            # 18 - الحالة
        ]
        
        # مواضع أعمدة blank في النسق الأصلي (للحذف)
        self.blank_positions = [1, 10, 20, 21, 22, 23, 24]  # مواضع أعمدة blank
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processing = False
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        
        # خيارات نوع الملف
        self.file_type = tk.StringVar(value="both")  # both, csv, txt

        # خيارات تصدير الملف المستخرج
        self.output_format = tk.StringVar(value="csv")  # csv, txt, both
        
        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 CSV Cleaner Tool - No Blank Columns",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()

    def create_folder_section(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد ملفات CSV/TXT:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True)
        
        ttk.Checkbutton(left_column, text="🔧 توحيد رؤوس الأعمدة (19 عمود بدون blank)", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🧹 تنظيف البيانات", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # قسم اختيار نوع الملف
        file_type_frame = ttk.LabelFrame(right_column, text="📄 نوع الملفات", padding=5)
        file_type_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Radiobutton(file_type_frame, text="📊 CSV و TXT معاً",
                       variable=self.file_type, value="both").pack(anchor='w')
        ttk.Radiobutton(file_type_frame, text="📈 ملفات CSV فقط",
                       variable=self.file_type, value="csv").pack(anchor='w')
        ttk.Radiobutton(file_type_frame, text="📝 ملفات TXT فقط",
                       variable=self.file_type, value="txt").pack(anchor='w')

        # قسم اختيار تنسيق الإخراج
        output_format_frame = ttk.LabelFrame(right_column, text="💾 تنسيق الإخراج", padding=5)
        output_format_frame.pack(fill='x', pady=(5, 0))

        ttk.Radiobutton(output_format_frame, text="📈 CSV فقط",
                       variable=self.output_format, value="csv").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📝 TXT فقط",
                       variable=self.output_format, value="txt").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📊 CSV و TXT معاً",
                       variable=self.output_format, value="both").pack(anchor='w')

    def create_control_section(self):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='both', expand=True)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # معلومات الحالة
        ttk.Label(control_frame, text="📄 الملف الحالي:").pack(anchor='w', pady=(10, 0))
        ttk.Label(control_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w')
        
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV/TXT")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            txt_files = list(Path(folder).glob("*.txt"))
            total_files = len(csv_files) + len(txt_files)
            self.status.set(f"تم اختيار المجلد - وجد {total_files} ملف")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            input_path = Path(self.input_folder.get())
            
            # جمع الملفات حسب النوع المختار
            files_to_process = []
            file_type = self.file_type.get()
            
            if file_type == "both":
                files_to_process.extend(list(input_path.glob("*.csv")))
                files_to_process.extend(list(input_path.glob("*.txt")))
            elif file_type == "csv":
                files_to_process = list(input_path.glob("*.csv"))
            elif file_type == "txt":
                files_to_process = list(input_path.glob("*.txt"))
            
            if not files_to_process:
                file_types_msg = {
                    "both": "CSV أو TXT",
                    "csv": "CSV", 
                    "txt": "TXT"
                }
                messagebox.showwarning("تنبيه", f"لا توجد ملفات {file_types_msg[file_type]} في المجلد")
                return
            
            total_files = len(files_to_process)
            processed_files = 0
            
            for i, data_file in enumerate(files_to_process):
                if not self.processing:
                    break
                
                # تحديث الواجهة
                self.current_file.set(data_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                self.progress.set((i / total_files) * 100)
                self.root.update_idletasks()
                
                # معالجة الملف
                if self.process_file_no_blank(data_file):
                    processed_files += 1
                
                # تحديث التقدم
                self.progress.set(((i + 1) / total_files) * 100)
                self.root.update_idletasks()
            
            if self.processing:
                self.status.set(f"✅ تم الانتهاء! معالج {processed_files} من {total_files} ملف")
                messagebox.showinfo("مكتمل", f"تم تنظيف {processed_files} ملف بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_file_no_blank(self, input_file):
        """معالجة ملف مع حذف أعمدة blank من الرؤوس والبيانات"""
        try:
            print(f"بدء معالجة: {input_file.name}")
            
            # قراءة الملف
            data_rows = []
            file_extension = input_file.suffix.lower()
            
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                # قراءة المحتوى كاملاً لإصلاح الفواصل المتعددة
                content = f.read()

                # كشف الفاصل
                delimiter = ',' if content.count(',') >= content.count(';') else ';'
                if file_extension == '.txt':
                    delimiter = '\t' if content.count('\t') > content.count(',') else delimiter

                # إصلاح الفواصل المتعددة مع الاحتفاظ بعلامات التنصيص
                if self.clean_data.get():
                    lines = content.split('\n')
                    fixed_lines = []
                    for line in lines:
                        if line.strip():
                            # إصلاح الفواصل المتعددة مع الاحتفاظ بعلامات التنصيص
                            fixed_line = self.fix_multiple_commas_in_line(line)
                            fixed_lines.append(fixed_line)
                        else:
                            fixed_lines.append(line)
                    content = '\n'.join(fixed_lines)

                # تحويل المحتوى المصحح إلى CSV reader
                from io import StringIO
                csv_reader = csv.reader(StringIO(content), delimiter=delimiter)
                data_rows = list(csv_reader)
            
            print(f"تم قراءة {len(data_rows)} سطر من {input_file.name}")
            
            if len(data_rows) <= 1:
                print(f"تحذير: {input_file.name} لا يحتوي على بيانات")
                return False
            
            # تنظيف البيانات
            if self.clean_data.get():
                data_rows = self.clean_data_simple(data_rows)
            
            # توحيد الرؤوس وحذف أعمدة blank
            if self.standardize_headers.get():
                print(f"📋 قبل توحيد الرؤوس: {len(data_rows)} سطر")
                if data_rows:
                    print(f"📋 رؤوس الأعمدة الأصلية: {data_rows[0][:5]}...")  # أول 5 أعمدة
                data_rows = self.apply_standard_format_no_blank(data_rows)
                print(f"📋 بعد توحيد الرؤوس: {len(data_rows)} سطر")
            
            # إزالة المكررات
            if self.remove_duplicates.get():
                original_count = len(data_rows)
                data_rows = self.remove_duplicates_simple(data_rows)
                print(f"تم حذف {original_count - len(data_rows)} مكرر")
            
            # التحقق من وجود بيانات بعد المعالجة
            if len(data_rows) <= 1:
                print(f"تحذير: لا توجد بيانات بعد المعالجة في {input_file.name}")
                return False
            
            # تشخيص البيانات قبل الحفظ
            print(f"📊 البيانات قبل الحفظ: {len(data_rows)} سطر, {len(data_rows[0]) if data_rows else 0} عمود")
            print(f"💾 تنسيق الإخراج: {self.output_format.get()}")
            print(f"📁 مجلد الإخراج: {self.output_folder.get()}")

            # حفظ الملف حسب التنسيق المختار
            saved_files = self.save_output_files(data_rows, input_file.stem)

            if saved_files:
                files_info = ", ".join([f.name for f in saved_files])
                print(f"✅ تم حفظ: {files_info} - {len(data_rows)-1} سجل - {len(data_rows[0])} عمود")
                return True
            else:
                print(f"❌ فشل في حفظ الملفات")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في معالجة {input_file.name}: {str(e)}")
            return False

    def save_output_files(self, data_rows, base_filename):
        """حفظ الملفات بالتنسيقات المختارة"""
        saved_files = []
        output_format = self.output_format.get()

        print(f"💾 بدء حفظ الملفات...")
        print(f"💾 تنسيق الإخراج: {output_format}")
        print(f"💾 اسم الملف الأساسي: {base_filename}")
        print(f"💾 عدد الأسطر للحفظ: {len(data_rows)}")

        try:
            if output_format == "csv" or output_format == "both":
                # حفظ ملف CSV
                csv_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.csv"
                print(f"💾 حفظ ملف CSV: {csv_file}")
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f, delimiter=',', quoting=csv.QUOTE_MINIMAL)
                    writer.writerows(data_rows)
                saved_files.append(csv_file)
                print(f"✅ تم حفظ ملف CSV بنجاح")

            if output_format == "txt" or output_format == "both":
                # حفظ ملف TXT
                txt_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.txt"
                print(f"💾 حفظ ملف TXT: {txt_file}")
                with open(txt_file, 'w', encoding='utf-8') as f:
                    for row in data_rows:
                        # استخدام Tab كفاصل في ملفات TXT
                        line = '\t'.join(str(cell) for cell in row)
                        f.write(line + '\n')
                saved_files.append(txt_file)
                print(f"✅ تم حفظ ملف TXT بنجاح")

            return saved_files

        except Exception as e:
            print(f"❌ خطأ في حفظ الملفات: {str(e)}")
            return []

    def fix_multiple_commas_in_line(self, line):
        """إصلاح الفواصل المتعددة المتتالية في السطر مع الاحتفاظ بعلامات التنصيص"""
        # الجولة الأولى: إصلاح الفواصل من 6 إلى 2
        line = line.replace(',,,,,,', ',')  # 6 فواصل
        line = line.replace(',,,,,', ',')   # 5 فواصل
        line = line.replace(',,,,', ',')    # 4 فواصل
        line = line.replace(',,,', ',')     # 3 فواصل
        line = line.replace(',,', ',')      # فاصلتان

        # الجولة الثانية: تكرار العملية لضمان عدم وجود فواصل زائدة
        line = line.replace(',,,,,,', ',')  # 6 فواصل
        line = line.replace(',,,,,', ',')   # 5 فواصل
        line = line.replace(',,,,', ',')    # 4 فواصل
        line = line.replace(',,,', ',')     # 3 فواصل
        line = line.replace(',,', ',')      # فاصلتان

        return line

    def clean_data_simple(self, data_rows):
        """تنظيف البيانات - نسخة مبسطة"""
        cleaned_rows = []
        for row in data_rows:
            cleaned_row = []
            for cell in row:
                if isinstance(cell, str):
                    # إزالة علامات الاقتباس والمسافات
                    cell = cell.strip().replace('"', '').replace("'", '')
                    # تنظيف الرموز الغريبة
                    cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cell)
                    cell = re.sub(r'\s+', ' ', cell).strip()
                cleaned_row.append(cell)
            cleaned_rows.append(cleaned_row)
        return cleaned_rows

    def apply_standard_format_no_blank(self, data_rows):
        """تطبيق النسق الثابت - نسخة مبسطة"""
        print(f"🔧 بدء تطبيق النسق الثابت...")

        if not data_rows:
            print(f"⚠️ لا توجد بيانات للمعالجة")
            return [self.standard_headers]

        print(f"🔧 عدد الأسطر الأصلية: {len(data_rows)}")
        print(f"🔧 عدد الأعمدة الأصلية: {len(data_rows[0]) if data_rows else 0}")

        # إنشاء ملف جديد بالنسق الثابت (19 عمود بدون blank)
        standardized_data = [self.standard_headers]

        # معالجة البيانات - نسخ البيانات مباشرة مع تحديد 19 عمود
        processed_rows = 0
        for row_num, row in enumerate(data_rows[1:], 1):  # تخطي الرؤوس الأصلية
            # إنشاء صف جديد بـ 19 عمود
            new_row = [''] * len(self.standard_headers)

            # نسخ البيانات المتاحة (أول 19 عمود أو أقل)
            for i, cell in enumerate(row):
                if i < len(new_row):
                    clean_cell = str(cell).replace('"', '').strip()
                    new_row[i] = clean_cell

            # إضافة الصف إذا كان يحتوي على بيانات
            if any(cell.strip() for cell in new_row):
                standardized_data.append(new_row)
                processed_rows += 1
            else:
                print(f"⚠️ تم تجاهل السطر {row_num} (فارغ)")

        print(f"🔧 تم معالجة {processed_rows} سطر من أصل {len(data_rows)-1}")
        print(f"🔧 النتيجة النهائية: {len(standardized_data)} سطر (مع الرؤوس)")

        return standardized_data

    def remove_duplicates_simple(self, data_rows):
        """إزالة المكررات - نسخة مبسطة"""
        if len(data_rows) <= 1:
            return data_rows
        
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            # إنشاء مفتاح فريد للصف
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = NoBlankCSVCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

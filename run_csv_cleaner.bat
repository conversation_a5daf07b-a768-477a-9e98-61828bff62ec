@echo off
echo ========================================
echo    CSV Cleaner Tool - اداة تنظيف CSV
echo ========================================
echo.
echo Starting CSV Cleaner Tool...
echo جاري تشغيل اداة تنظيف ملفات CSV...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo.
    echo Please install Python 3.6+ from: https://python.org
    echo يرجى تثبيت Python 3.6+ من: https://python.org
    pause
    exit /b 1
)

REM Run the CSV Cleaner Tool
python csv_cleaner_tool.py

REM Check if the script ran successfully
if errorlevel 1 (
    echo.
    echo ERROR: Failed to run CSV Cleaner Tool
    echo خطأ: فشل في تشغيل اداة تنظيف CSV
    echo.
    pause
) else (
    echo.
    echo CSV Cleaner Tool closed successfully
    echo تم إغلاق اداة تنظيف CSV بنجاح
)

pause

@echo off
chcp 65001 >nul
title Data Search Tool - أداة البحث في البيانات

echo.
echo ========================================
echo    🔍 Data Search Tool
echo    أداة البحث في البيانات الاجتماعية
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

REM Check if search tool file exists
if not exist "txt_to_csv_converter.py" (
    echo ❌ خطأ: ملف txt_to_csv_converter.py غير موجود
    pause
    exit /b 1
)

echo 🚀 جاري تشغيل أداة البحث...
echo.

REM Run the search tool
python txt_to_csv_converter.py

echo.
echo ✅ تم إغلاق أداة البحث
pause

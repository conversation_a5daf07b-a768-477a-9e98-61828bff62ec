#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار البيانات المصرية مع الترميز المختلط
"""

import tkinter as tk
from txt_to_csv_converter import ConversionApp
import os

def test_data_processing():
    """اختبار معالجة البيانات المصرية"""
    print("🧪 اختبار معالجة البيانات المصرية...")
    
    # إنشاء التطبيق
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    app = ConversionApp(root)
    
    # اختبار تنظيف النصوص
    test_texts = [
        'غ¦غ¦ ط¹ظ…ط±ظˆ',
        'ï±‍ï±‍ï±‍ï±‍ï±‍',
        'ط£ط¨ظˆ ط¹ظ…ط±',
        'A C Mahmoud'
    ]
    
    print("\n📝 اختبار تنظيف النصوص:")
    for text in test_texts:
        cleaned = app._clean_text(text)
        print(f"  الأصلي: '{text}' → المنظف: '{cleaned}'")
    
    # اختبار البحث في أرقام الهواتف
    phone_tests = [
        ('+201002258912', '+201002258912'),
        ('01002258912', '+201002258912'),
        ('201002258912', '+201002258912'),
        ('1002258912', '+201002258912'),
    ]
    
    print("\n📞 اختبار البحث في أرقام الهواتف:")
    for search, target in phone_tests:
        match = app._phone_number_match(search, target)
        print(f"  البحث: '{search}' في '{target}' → {match}")
    
    # اختبار البحث المرن
    flexible_tests = [
        ('100007319070330', '100007319070330'),
        ('flfl0x', '<EMAIL>'),
        ('amer', 'amer.aboomr'),
        ('Mahmoud', 'A C Mahmoud'),
    ]
    
    print("\n🔍 اختبار البحث المرن:")
    for search, target in flexible_tests:
        match = app._flexible_search_match(search, target)
        print(f"  البحث: '{search}' في '{target}' → {match}")
    
    # اختبار معالجة سطر CSV
    sample_line = '"100007319070330","","","+201002258912","","","ï±‍ï±‍ï±‍ï±‍ï±‍","ï±‍ï±‍ï±‍ï±‍ï±‍","male","https://www.facebook.com/flfl0x","","flfl0x"'
    
    print(f"\n📊 اختبار معالجة سطر CSV:")
    print(f"  السطر الأصلي: {sample_line[:100]}...")
    
    try:
        import csv
        import io
        
        line_io = io.StringIO(sample_line)
        csv_reader = csv.reader(line_io, delimiter=',', quotechar='"')
        row = next(csv_reader, None)
        
        if row:
            print(f"  عدد الحقول: {len(row)}")
            print(f"  Facebook ID: '{row[0]}'")
            print(f"  الهاتف: '{row[3]}'")
            print(f"  الاسم الأول: '{row[6]}' → منظف: '{app._clean_text(row[6])}'")
            print(f"  الرابط: '{row[9]}'")
            print(f"  اليوزرنيم: '{row[11]}'")
        else:
            print("  ❌ فشل في تحليل السطر")
            
    except Exception as e:
        print(f"  ❌ خطأ في معالجة CSV: {e}")
    
    # اختبار البحث في السطر الكامل
    print(f"\n🎯 اختبار البحث في السطر الكامل:")
    if row:
        search_terms = ['100007319070330', '+201002258912', 'flfl0x', 'facebook.com']
        
        for term in search_terms:
            found = False
            for cell in row:
                cell_clean = app._clean_text(str(cell)).lower()
                term_lower = term.lower()
                
                if (term_lower in str(cell).lower() or 
                    term_lower in cell_clean or
                    app._flexible_search_match(term_lower, str(cell).lower()) or
                    app._phone_number_match(term_lower, str(cell))):
                    found = True
                    break
            
            print(f"  البحث عن '{term}': {'✅ موجود' if found else '❌ غير موجود'}")
    
    print("\n🎉 انتهى الاختبار!")
    root.destroy()

def test_file_processing():
    """اختبار معالجة الملف الكامل"""
    print("\n📁 اختبار معالجة الملف...")
    
    # التحقق من وجود ملفات الاختبار
    files_to_check = [
        'sample_egyptian_data.csv',
        'search_ids.txt',
        'search_phones.txt'
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  ✅ {filename} ({size} bytes)")
        else:
            print(f"  ❌ {filename} غير موجود")
    
    # قراءة ملف البيانات
    if os.path.exists('sample_egyptian_data.csv'):
        print(f"\n📖 قراءة ملف البيانات:")
        try:
            with open('sample_egyptian_data.csv', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"  عدد الأسطر: {len(lines)}")
                
                for i, line in enumerate(lines[:3], 1):
                    print(f"  السطر {i}: {line[:100]}...")
                    
        except Exception as e:
            print(f"  ❌ خطأ في قراءة الملف: {e}")
    
    # قراءة ملف البحث
    if os.path.exists('search_ids.txt'):
        print(f"\n🔍 قراءة ملف البحث:")
        try:
            with open('search_ids.txt', 'r', encoding='utf-8') as f:
                ids = [line.strip() for line in f if line.strip()]
                print(f"  عدد المعرفات: {len(ids)}")
                for id in ids:
                    print(f"  - {id}")
                    
        except Exception as e:
            print(f"  ❌ خطأ في قراءة ملف البحث: {e}")

if __name__ == "__main__":
    print("🚀 بدء اختبار البيانات المصرية...")
    test_data_processing()
    test_file_processing()
    
    print(f"\n💡 نصائح للاختبار:")
    print(f"  1. استخدم ملف 'sample_egyptian_data.csv' كقاعدة بيانات")
    print(f"  2. استخدم ملف 'search_ids.txt' للبحث بالمعرفات")
    print(f"  3. استخدم ملف 'search_phones.txt' للبحث بالهواتف")
    print(f"  4. جرب البحث بـ 'flfl0x' أو 'amer' في حقول النص")
    print(f"  5. تأكد من اختيار الحقول المطلوبة في النتائج")
    
    print(f"\n🎯 الآن شغل البرنامج الرئيسي واختبر البحث!")
    print(f"  python run_app.py")

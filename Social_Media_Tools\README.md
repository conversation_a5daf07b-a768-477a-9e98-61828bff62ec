# 🚀 Social Media Data Tools
## مجموعة أدوات البيانات الاجتماعية

### 📋 نظرة عامة
مجموعة شاملة من الأدوات المتخصصة في معالجة والبحث في البيانات الاجتماعية الضخمة. تم تصميم هذه الأدوات لحل مشاكل البيانات غير المنظمة والبحث في قواعد البيانات الكبيرة بكفاءة عالية.

### 🎯 الأدوات المتاحة

#### 1️⃣ المنصة الرئيسية (Main Launcher)
**التشغيل**: دبل كليك على `🚀 تشغيل الأدوات.bat`
- واجهة موحدة لجميع الأدوات
- فحص حالة الأدوات المتاحة
- تشغيل الأدوات بنقرة واحدة

#### 2️⃣ أداة تنظيف CSV
**التشغيل**: دبل كليك على `🧹 تنظيف CSV.bat`
- تنظيف ملفات CSV الكبيرة
- توحيد رؤوس الأعمدة
- إزالة السجلات المكررة
- تقسيم الملفات الضخمة

#### 3️⃣ أداة البحث في البيانات
**التشغيل**: دبل كليك على `🔍 البحث في البيانات.bat`
- البحث في قواعد البيانات الضخمة
- دعم ملفات TXT, CSV, DB
- فلترة متقدمة للنتائج

### 🚀 البدء السريع

#### الطريقة الأسهل:
1. **دبل كليك** على `🚀 تشغيل الأدوات.bat`
2. اختر الأداة المطلوبة من المنصة الرئيسية
3. اضغط "تشغيل"

#### للاستخدام المباشر:
- `🧹 تنظيف CSV.bat` - لتنظيف ملفات CSV
- `🔍 البحث في البيانات.bat` - للبحث في البيانات

### 📁 محتويات المجلد

```
Social_Media_Tools/
├── 🚀 تشغيل الأدوات.bat          # المنصة الرئيسية
├── 🧹 تنظيف CSV.bat              # أداة تنظيف CSV
├── 🔍 البحث في البيانات.bat       # أداة البحث
├── main_launcher.py              # كود المنصة الرئيسية
├── csv_cleaner_tool.py           # كود أداة تنظيف CSV
├── txt_to_csv_converter.py       # كود أداة البحث
├── system_check.py               # أداة فحص النظام
├── test_sample_data.csv          # ملف اختبار
├── README.md                     # هذا الملف
├── README_CSV_Cleaner.md         # دليل أداة تنظيف CSV
├── QUICK_START.md                # دليل البدء السريع
└── دليل الاستخدام.md             # دليل شامل بالعربية
```

### 🔧 متطلبات النظام

#### الحد الأدنى:
- **Python 3.6+** (مطلوب)
- **Windows 7+** / macOS / Linux
- **4GB RAM**
- **1GB مساحة فارغة**

#### للبيانات الضخمة:
- **8GB+ RAM**
- **SSD** (موصى به)
- **مساحة = ضعف حجم البيانات**

### 💡 سير العمل الموصى به

#### للبيانات الجديدة:
1. **ابدأ بأداة تنظيف CSV** 🧹
   - نظف وحسن تنسيق البيانات
   - وحد رؤوس الأعمدة
   - قسم الملفات الكبيرة

2. **استخدم أداة البحث** 🔍
   - ابحث في البيانات المنظفة
   - طبق فلاتر متقدمة
   - صدر النتائج

#### للبيانات المنظمة:
- **استخدم أداة البحث مباشرة** 🔍

### 🛠️ حل المشاكل الشائعة

#### "Python غير مثبت"
1. حمل من [python.org](https://python.org)
2. تأكد من "Add to PATH"
3. أعد تشغيل الكمبيوتر

#### "نفدت الذاكرة"
1. أغلق البرامج الأخرى
2. استخدم تقسيم الملفات
3. قلل حجم البيانات

#### "البرنامج لا يستجيب"
1. انتظر (قد يعالج بيانات كبيرة)
2. راقب Task Manager
3. استخدم أزرار الإيقاف

### 📞 المساعدة والدعم

#### للمساعدة السريعة:
1. راجع `دليل الاستخدام.md`
2. اقرأ `QUICK_START.md`
3. جرب `README_CSV_Cleaner.md`

#### للمشاكل التقنية:
1. شغل `system_check.py` لفحص النظام
2. تحقق من رسائل الخطأ
3. راجع متطلبات النظام

### 🎉 ابدأ الآن!

**دبل كليك على** `🚀 تشغيل الأدوات.bat` **وابدأ رحلتك مع البيانات الاجتماعية!**

---

### 📈 المميزات الرئيسية

✅ **واجهات مستخدم سهلة** - تعمل بالدبل كليك  
✅ **معالجة البيانات الضخمة** - حتى 45 مليون سجل  
✅ **تنظيف تلقائي** - للبيانات غير المنظمة  
✅ **بحث متقدم** - مع فلاتر ذكية  
✅ **تصدير سهل** - نتائج بصيغة CSV  
✅ **دعم متعدد اللغات** - عربي وإنجليزي  

### 🔄 التحديثات

هذه الأدوات في تطوير مستمر. احتفظ بنسخة احتياطية من بياناتك دائماً!

---
**تم تطوير هذه الأدوات لتحسين كفاءة البحث في البيانات الضخمة وتوفير الوقت والجهد في تنظيف البيانات.**

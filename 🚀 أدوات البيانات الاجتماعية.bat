@echo off
chcp 65001 >nul
title Social Media Data Tools - أدوات البيانات الاجتماعية

echo.
echo ========================================
echo    🚀 Social Media Data Tools
echo    أدوات البيانات الاجتماعية
echo ========================================
echo.
echo 📁 فتح مجلدات الأدوات...
echo.

REM فتح مجلد أداة تنظيف CSV
if exist "CSV_Cleaner_Tool" (
    echo ✅ تم العثور على أداة تنظيف CSV
    start "" "CSV_Cleaner_Tool"
) else (
    echo ❌ مجلد CSV_Cleaner_Tool غير موجود
)

REM فتح مجلد أداة البحث
if exist "Data_Search_Tool" (
    echo ✅ تم العثور على أداة البحث
    start "" "Data_Search_Tool"
) else (
    echo ❌ مجلد Data_Search_Tool غير موجود
)

echo.
echo 💡 تعليمات الاستخدام:
echo.
echo 🧹 لتنظيف ملفات CSV:
echo    ادخل مجلد CSV_Cleaner_Tool
echo    دبل كليك على: 🧹 تشغيل أداة تنظيف CSV.bat
echo.
echo 🔍 للبحث في البيانات:
echo    ادخل مجلد Data_Search_Tool  
echo    دبل كليك على: 🔍 تشغيل أداة البحث.bat
echo.
echo 📋 سير العمل الموصى به:
echo    1. ابدأ بتنظيف البيانات (CSV Cleaner)
echo    2. ثم ابحث في البيانات المنظفة (Data Search)
echo.

timeout /t 5 >nul

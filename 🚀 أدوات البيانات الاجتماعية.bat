@echo off
title Social Media Data Tools

echo.
echo ========================================
echo    Social Media Data Tools
echo ========================================
echo.
echo Opening tools folders...
echo.

REM Open CSV Cleaner Tool folder
if exist "CSV_Cleaner_Tool" (
    echo Found CSV Cleaner Tool
    start "" "CSV_Cleaner_Tool"
) else (
    echo ERROR: CSV_Cleaner_Tool folder not found
)

REM Open Data Search Tool folder
if exist "Data_Search_Tool" (
    echo Found Data Search Tool
    start "" "Data_Search_Tool"
) else (
    echo ERROR: Data_Search_Tool folder not found
)

echo.
echo Usage Instructions:
echo.
echo For CSV Cleaning:
echo   Go to CSV_Cleaner_Tool folder
echo   Double-click: run_csv_cleaner.bat
echo.
echo For Data Search:
echo   Go to Data_Search_Tool folder
echo   Double-click: run_data_search.bat
echo.
echo Recommended Workflow:
echo   1. Clean your data first (CSV Cleaner)
echo   2. Then search in cleaned data (Data Search)
echo.

timeout /t 5 >nul

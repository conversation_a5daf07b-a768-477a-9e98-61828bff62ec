#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل أداة البحث في البيانات الضخمة
Data Search Tool Runner
"""

import tkinter as tk
from txt_to_csv_converter import ConversionApp

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # تطبيق ثيم حديث
        try:
            root.tk.call("source", "azure.tcl")
            root.tk.call("set_theme", "light")
        except:
            pass  # إذا لم يكن الثيم متاحاً
        
        # إنشاء التطبيق
        app = ConversionApp(root)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()

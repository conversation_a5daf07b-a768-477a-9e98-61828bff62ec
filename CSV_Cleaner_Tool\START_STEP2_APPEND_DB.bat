@echo off
title STEP 2 - Append to Database

echo ========================================
echo    STEP 2 - APPEND TO DATABASE
echo ========================================
echo.
echo Starting Database Append Process...
echo.
echo Features:
echo - Appends new data to existing fulldata.db
echo - Creates fulldata.db if it doesn't exist
echo - Handles pipe-delimited clean files
echo - Maintains existing data while adding new
echo.

python STEP2_APPEND_DB_INSERTER.py

if errorlevel 1 (
    echo.
    echo Failed to start Database Append Tool
    echo Please make sure Python is installed
    pause
)

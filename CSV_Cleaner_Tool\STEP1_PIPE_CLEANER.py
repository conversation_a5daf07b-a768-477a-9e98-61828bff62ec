#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 المرحلة الأولى: تنظيف البيانات باستخدام Pipe Delimiter
STEP 1: Data Cleaning with Pipe Delimiter
"""

import os
import re
import csv
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
from pathlib import Path

class PipeDataCleaner:
    def __init__(self, root):
        """تهيئة أداة التنظيف"""
        self.root = root
        self.root.title("🧹 المرحلة الأولى: تنظيف البيانات - Pipe Delimiter")
        self.root.geometry("800x600")
        self.root.configure(pady=10, padx=10)
        
        # الفاصل البديل
        self.PIPE_DELIMITER = "|"
        
        # النسق الأصلي مع الأعمدة x (26 عمود)
        self.ORIGINAL_HEADERS = [
            'facebook_id', 'x1', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'x2', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 'country',
            'education', 'user', 'x3', 'x4', 'x5', 'x6', 'x7', 'status'
        ]

        # النسق النهائي بعد حذف الأعمدة x (19 عمود)
        self.FINAL_HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown',
            'country', 'education', 'user', 'status'
        ]

        # مؤشرات الأعمدة المطلوبة (بدون x)
        self.KEEP_COLUMNS = [0, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 25]
        
        # أنماط البيانات
        self.PATTERNS = {
            'facebook_id': r'\d{10,20}',
            'phone': r'\+?\d{10,15}',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'url': r'https?://[^\s|]+',
            'location': r'[A-Z][a-z]+\s+[A-Z][a-z]+'
        }
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.processing = False
        
        # خيارات الإخراج
        self.output_format = tk.StringVar(value="txt")  # txt أو csv
        
        self.setup_ui()

    def setup_ui(self):
        """إعداد الواجهة"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 المرحلة الأولى: تنظيف البيانات - Pipe Delimiter",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # معلومات
        info_frame = ttk.LabelFrame(self.root, text="ℹ️ معلومات", padding=10)
        info_frame.pack(fill='x', pady=(0, 10))
        
        info_text = f"الفاصل البديل: {self.PIPE_DELIMITER}\nاستبدال الفواصل العادية والمتعددة بـ | للتوافق الأمثل"
        ttk.Label(info_frame, text=info_text).pack(anchor='w')
        
        # المجلدات
        self.create_folder_section()
        
        # الخيارات
        self.create_options_section()
        
        # التحكم
        self.create_control_section()
        
        # النتائج
        self.create_results_section()

    def create_folder_section(self):
        """قسم المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد البيانات الخام:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد البيانات النظيفة:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # تنسيق الإخراج
        ttk.Label(options_frame, text="💾 تنسيق الملفات النظيفة:").pack(anchor='w')
        
        format_frame = ttk.Frame(options_frame)
        format_frame.pack(fill='x', pady=5)
        
        ttk.Radiobutton(format_frame, text="📝 TXT مع | (أسرع للإدراج)", 
                       variable=self.output_format, value="txt").pack(side='left', padx=(0, 20))
        ttk.Radiobutton(format_frame, text="📈 CSV مع | (متوافق أكثر)", 
                       variable=self.output_format, value="csv").pack(side='left')

    def create_control_section(self):
        """قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🧹 بدء تنظيف البيانات", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # الحالة
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def create_results_section(self):
        """قسم النتائج"""
        results_frame = ttk.LabelFrame(self.root, text="📊 نتائج التنظيف", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log(self, message):
        """إضافة رسالة للنتائج"""
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد البيانات الخام")
        if folder:
            self.input_folder.set(folder)
            files = list(Path(folder).glob("*.*"))
            self.status.set(f"تم اختيار المجلد - وجد {len(files)} ملف")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد البيانات النظيفة")
        if folder:
            self.output_folder.set(folder)

    def start_cleaning(self):
        """بدء التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_thread(self):
        """معالجة التنظيف"""
        try:
            self.log("🧹 بدء تنظيف البيانات باستخدام Pipe Delimiter...")
            self.log(f"| الفاصل البديل: {self.PIPE_DELIMITER}")
            
            input_path = Path(self.input_folder.get())
            files = list(input_path.glob("*.csv")) + list(input_path.glob("*.txt"))
            
            if not files:
                self.log("❌ لا توجد ملفات للمعالجة")
                return
            
            self.log(f"📊 عدد الملفات: {len(files)}")
            
            total_records = 0
            
            for i, file_path in enumerate(files):
                if not self.processing:
                    break
                
                self.log(f"\n📄 معالجة الملف {i+1}/{len(files)}: {file_path.name}")
                self.status.set(f"معالجة {file_path.name}")
                self.progress.set((i / len(files)) * 100)
                
                records = self.clean_single_file(file_path)
                total_records += records
                
                self.root.update_idletasks()
            
            if self.processing:
                self.log(f"\n🎉 تم الانتهاء من التنظيف!")
                self.log(f"📊 إجمالي السجلات المنظفة: {total_records}")
                self.log(f"💾 تنسيق الإخراج: {self.output_format.get().upper()}")
                self.log(f"📁 مجلد البيانات النظيفة: {self.output_folder.get()}")
                self.status.set("✅ تم الانتهاء من التنظيف!")
                messagebox.showinfo("مكتمل", f"تم تنظيف {total_records} سجل بنجاح!")
            
        except Exception as e:
            self.log(f"❌ خطأ عام: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def clean_single_file(self, file_path):
        """تنظيف ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            self.log(f"📋 قراءة {len(lines)} سطر")
            
            cleaned_data = []
            
            for line in lines:
                # تنظيف السطر باستخدام Pipe
                cleaned_line = self.clean_line_with_pipe(line)
                
                if cleaned_line and any(cell.strip() for cell in cleaned_line):
                    cleaned_data.append(cleaned_line)
            
            # حفظ البيانات النظيفة
            if cleaned_data:
                output_file = self.save_cleaned_data(cleaned_data, file_path.stem)
                self.log(f"💾 تم حفظ {len(cleaned_data)} سجل في: {output_file.name}")
                return len(cleaned_data)
            else:
                self.log(f"⚠️ لا توجد بيانات صالحة في الملف")
                return 0
            
        except Exception as e:
            self.log(f"❌ خطأ في معالجة {file_path.name}: {str(e)}")
            return 0

    def clean_line_with_pipe(self, line):
        """تنظيف السطر باستخدام Pipe Delimiter"""
        try:
            # 1️⃣ استبدال الفواصل المتعددة بـ |
            line = re.sub(r',{2,}', '|', line)

            # 2️⃣ استبدال الفواصل العادية بـ |
            line = re.sub(r',', '|', line)
            
            # 4️⃣ إزالة علامات التنصيص الزائدة
            line = re.sub(r'"', '', line)
            
            # 5️⃣ تنظيف المسافات الزائدة
            line = re.sub(r'\s+', ' ', line).strip()
            
            # 6️⃣ تقسيم البيانات
            parts = [part.strip() for part in line.split('|')]
            
            # 7️⃣ تطابق البيانات الفعلية مع النسق المطلوب
            standardized = [''] * len(self.FINAL_HEADERS)

            # بناءً على البيانات الفعلية:
            # 100018551915150||+201003609177|||Fares|Omar|male|https://www.facebook.com/fares.omar.33234|fares.omar.33234|Fares Omar|||||||<EMAIL>|
            # المؤشرات: 0:facebook_id, 2:phone, 6:first_name, 7:last_name, 8:gender, 9:link, 10:username, 11:fullname, 18:email

            if len(parts) >= 19:
                standardized[0] = parts[0] if len(parts) > 0 else ''   # facebook_id
                standardized[1] = parts[18] if len(parts) > 18 else '' # email
                standardized[2] = parts[2] if len(parts) > 2 else ''   # phone
                standardized[3] = parts[6] if len(parts) > 6 else ''   # first_name
                standardized[4] = parts[7] if len(parts) > 7 else ''   # last_name
                standardized[5] = parts[8] if len(parts) > 8 else ''   # gender
                standardized[6] = parts[9] if len(parts) > 9 else ''   # link
                standardized[7] = parts[10] if len(parts) > 10 else '' # username
                standardized[8] = parts[11] if len(parts) > 11 else '' # fullname
            else:
                # إذا كانت البيانات أقل من المتوقع
                for i, part in enumerate(parts):
                    if i < len(standardized):
                        standardized[i] = part
            
            # 9️⃣ محاولة استخراج البيانات الملتصقة إذا لزم الأمر
            if not any(standardized[:3]):  # إذا لم نحصل على facebook_id, email, phone
                extracted = self.extract_merged_data(line)
                if extracted:
                    return extracted
            
            return standardized
            
        except Exception as e:
            self.log(f"❌ خطأ في تنظيف السطر: {str(e)}")
            return [''] * len(self.HEADERS)

    def extract_merged_data(self, line):
        """استخراج البيانات الملتصقة"""
        separated = [''] * len(self.FINAL_HEADERS)
        
        try:
            # استخراج Facebook ID
            fb_match = re.search(self.PATTERNS['facebook_id'], line)
            if fb_match:
                separated[0] = fb_match.group()
                line = line.replace(fb_match.group(), '|', 1)
            
            # استخراج الهاتف
            phone_match = re.search(self.PATTERNS['phone'], line)
            if phone_match:
                separated[2] = phone_match.group()
                line = line.replace(phone_match.group(), '|', 1)
            
            # استخراج الإيميل
            email_match = re.search(self.PATTERNS['email'], line)
            if email_match:
                separated[1] = email_match.group()
                line = line.replace(email_match.group(), '|', 1)
            
            # استخراج الرابط
            url_match = re.search(self.PATTERNS['url'], line)
            if url_match:
                separated[8] = url_match.group()
                line = line.replace(url_match.group(), '|', 1)
            
            # استخراج الموقع
            location_match = re.search(self.PATTERNS['location'], line)
            if location_match:
                location_parts = location_match.group().split()
                if len(location_parts) >= 2:
                    separated[14] = location_parts[0]  # hometown
                    separated[15] = location_parts[1]  # country
                line = line.replace(location_match.group(), '|', 1)
            
            # معالجة النص المتبقي
            remaining_parts = [part.strip() for part in line.split('|') if part.strip()]
            
            # استخراج الاسم العربي
            for part in remaining_parts:
                if re.search(r'[\u0600-\u06FF]', part):
                    if not separated[10]:  # fullname
                        separated[10] = part
                        name_parts = part.split()
                        if len(name_parts) >= 2:
                            separated[5] = name_parts[0]  # first_name
                            separated[6] = name_parts[-1]  # last_name
                    break
            
            return separated
            
        except Exception as e:
            self.log(f"❌ خطأ في استخراج البيانات: {str(e)}")
            return [''] * len(self.FINAL_HEADERS)

    def save_cleaned_data(self, data, base_filename):
        """حفظ البيانات النظيفة"""
        output_format = self.output_format.get()
        
        if output_format == "txt":
            # حفظ كملف TXT بالفاصل |
            output_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                # كتابة الرؤوس النهائية (بدون x)
                f.write(self.PIPE_DELIMITER.join(self.FINAL_HEADERS) + '\n')
                # كتابة البيانات
                for row in data:
                    f.write(self.PIPE_DELIMITER.join(row) + '\n')
        else:
            # حفظ كملف CSV بالفاصل |
            output_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.csv"
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, delimiter='|')
                writer.writerow(self.FINAL_HEADERS)
                writer.writerows(data)
        
        return output_file


def main():
    """تشغيل أداة التنظيف"""
    root = tk.Tk()
    app = PipeDataCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

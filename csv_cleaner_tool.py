#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner & Formatter Tool
أداة تنظيف وتنسيق ملفات CSV للبيانات الاجتماعية

المميزات:
- تنظيف وتحسين تنسيق ملفات CSV
- توحيد رؤوس الأعمدة لجميع الملفات
- تصحيح الترميز والنصوص
- معالجة البيانات الضخمة بكفاءة
- واجهة مستخدم سهلة الاستخدام
"""

import os
import csv
import sys
import time
import shutil
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class CSVCleanerApp:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner & Formatter Tool - أداة تنظيف ملفات CSV")
        self.root.geometry("1000x700")
        self.root.configure(pady=10, padx=10)
        
        # المتغيرات الأساسية
        self.init_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # رؤوس الأعمدة الموحدة للبيانات الاجتماعية
        self.standard_headers = [
            'facebook_id',
            'first_name', 
            'last_name',
            'email',
            'phone',
            'gender',
            'birthday',
            'birthday_year',
            'location',
            'hometown',
            'country',
            'locale',
            'username',
            'profile_url',
            'education',
            'work',
            'religion',
            'relationship',
            'about_me',
            'interests'
        ]
        
    def init_variables(self):
        """تهيئة المتغيرات"""
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processed_files = tk.IntVar()
        self.total_files = tk.IntVar()
        self.processed_records = tk.IntVar()
        self.total_records = tk.IntVar()
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.fix_encoding = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        self.split_large_files = tk.BooleanVar(value=True)
        self.max_rows_per_file = tk.IntVar(value=1000000)  # مليون سجل لكل ملف
        
        # إحصائيات
        self.stats = {
            'total_input_files': 0,
            'total_output_files': 0,
            'total_records_processed': 0,
            'duplicates_removed': 0,
            'errors_fixed': 0
        }

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # قسم اختيار المجلدات
        self.create_folder_selection(main_frame)
        
        # قسم خيارات التنظيف
        self.create_cleaning_options(main_frame)
        
        # قسم التحكم والتقدم
        self.create_control_section(main_frame)
        
        # قسم الإحصائيات
        self.create_stats_section(main_frame)

    def create_folder_selection(self, parent):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(parent, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Label(input_frame, text="📂 مجلد ملفات CSV المراد تنظيفها:").pack(anchor='w')
        
        input_entry_frame = ttk.Frame(input_frame)
        input_entry_frame.pack(fill='x', pady=2)
        ttk.Entry(input_entry_frame, textvariable=self.input_folder, font=('Arial', 10)).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_entry_frame, text="تصفح", command=self.browse_input_folder).pack(side='right')
        
        # مجلد الإخراج
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Label(output_frame, text="💾 مجلد حفظ الملفات المنظفة:").pack(anchor='w')
        
        output_entry_frame = ttk.Frame(output_frame)
        output_entry_frame.pack(fill='x', pady=2)
        ttk.Entry(output_entry_frame, textvariable=self.output_folder, font=('Arial', 10)).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_entry_frame, text="تصفح", command=self.browse_output_folder).pack(side='right')

    def create_cleaning_options(self, parent):
        """إنشاء قسم خيارات التنظيف"""
        options_frame = ttk.LabelFrame(parent, text="⚙️ خيارات التنظيف والتحسين", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        ttk.Checkbutton(left_column, text="🔧 توحيد رؤوس الأعمدة", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🧹 تنظيف البيانات وإزالة الرموز الغريبة", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🔤 إصلاح ترميز النصوص", 
                       variable=self.fix_encoding).pack(anchor='w', pady=2)
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        ttk.Checkbutton(right_column, text="🗑️ إزالة السجلات المكررة", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)
        ttk.Checkbutton(right_column, text="✂️ تقسيم الملفات الكبيرة", 
                       variable=self.split_large_files).pack(anchor='w', pady=2)
        
        # إعداد حجم التقسيم
        split_frame = ttk.Frame(right_column)
        split_frame.pack(anchor='w', pady=2)
        ttk.Label(split_frame, text="📊 عدد السجلات لكل ملف:").pack(side='left')
        ttk.Entry(split_frame, textvariable=self.max_rows_per_file, width=10).pack(side='left', padx=(5, 0))

    def create_control_section(self, parent):
        """إنشاء قسم التحكم والتقدم"""
        control_frame = ttk.LabelFrame(parent, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning, style='Accent.TButton')
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح مجلد النتائج", 
                  command=self.open_output_folder).pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="🗑️ مسح النتائج", 
                  command=self.clear_output).pack(side='left')
        
        # شريط التقدم
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(progress_frame, text="📊 التقدم الإجمالي:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=2)
        
        # معلومات التقدم
        info_frame = ttk.Frame(control_frame)
        info_frame.pack(fill='x')
        
        # الملف الحالي
        ttk.Label(info_frame, text="📄 الملف الحالي:").pack(anchor='w')
        ttk.Label(info_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w', pady=(0, 5))
        
        # الحالة
        ttk.Label(info_frame, text="ℹ️ الحالة:").pack(anchor='w')
        ttk.Label(info_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def create_stats_section(self, parent):
        """إنشاء قسم الإحصائيات"""
        stats_frame = ttk.LabelFrame(parent, text="📈 الإحصائيات", padding=10)
        stats_frame.pack(fill='both', expand=True)
        
        # إطار الإحصائيات
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='both', expand=True)
        
        # الملفات
        ttk.Label(stats_grid, text="📁 الملفات المعالجة:", font=('Arial', 9, 'bold')).grid(
            row=0, column=0, sticky='w', padx=(0, 10), pady=2)
        self.files_label = ttk.Label(stats_grid, text="0 / 0", font=('Arial', 9))
        self.files_label.grid(row=0, column=1, sticky='w', pady=2)
        
        # السجلات
        ttk.Label(stats_grid, text="📊 السجلات المعالجة:", font=('Arial', 9, 'bold')).grid(
            row=1, column=0, sticky='w', padx=(0, 10), pady=2)
        self.records_label = ttk.Label(stats_grid, text="0", font=('Arial', 9))
        self.records_label.grid(row=1, column=1, sticky='w', pady=2)
        
        # المكررات المحذوفة
        ttk.Label(stats_grid, text="🗑️ المكررات المحذوفة:", font=('Arial', 9, 'bold')).grid(
            row=2, column=0, sticky='w', padx=(0, 10), pady=2)
        self.duplicates_label = ttk.Label(stats_grid, text="0", font=('Arial', 9))
        self.duplicates_label.grid(row=2, column=1, sticky='w', pady=2)
        
        # الأخطاء المصححة
        ttk.Label(stats_grid, text="🔧 الأخطاء المصححة:", font=('Arial', 9, 'bold')).grid(
            row=3, column=0, sticky='w', padx=(0, 10), pady=2)
        self.errors_label = ttk.Label(stats_grid, text="0", font=('Arial', 9))
        self.errors_label.grid(row=3, column=1, sticky='w', pady=2)

    def browse_input_folder(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            # عد ملفات CSV في المجلد
            csv_files = list(Path(folder).glob("*.csv"))
            self.total_files.set(len(csv_files))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")

    def browse_output_folder(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد حفظ النتائج")
        if folder:
            self.output_folder.set(folder)
            self.status.set("تم تحديد مجلد الإخراج")

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        # التحقق من المجلدات
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        # التحقق من وجود ملفات CSV
        input_path = Path(self.input_folder.get())
        csv_files = list(input_path.glob("*.csv"))
        
        if not csv_files:
            messagebox.showwarning("تنبيه", "لا توجد ملفات CSV في المجلد المحدد")
            return
        
        # تحديث حالة الأزرار
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        # إعادة تعيين الإحصائيات
        self.reset_stats()
        
        # بدء المعالجة في thread منفصل
        self.processing = True
        Thread(target=self.cleaning_thread, args=(csv_files,), daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")
        
    def reset_stats(self):
        """إعادة تعيين الإحصائيات"""
        self.progress.set(0)
        self.processed_files.set(0)
        self.processed_records.set(0)
        self.stats = {
            'total_input_files': 0,
            'total_output_files': 0,
            'total_records_processed': 0,
            'duplicates_removed': 0,
            'errors_fixed': 0
        }
        self.update_stats_display()

    def cleaning_thread(self, csv_files):
        """معالجة ملفات CSV في thread منفصل"""
        try:
            total_files = len(csv_files)
            self.total_files.set(total_files)

            # إنشاء مجلد الإخراج إذا لم يكن موجوداً
            output_path = Path(self.output_folder.get())
            output_path.mkdir(exist_ok=True)

            # معالجة كل ملف
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break

                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}: {csv_file.name}")

                # معالجة الملف
                self.process_csv_file(csv_file, output_path)

                # تحديث التقدم
                self.processed_files.set(i + 1)
                progress = ((i + 1) / total_files) * 100
                self.progress.set(progress)
                self.update_stats_display()

            # انتهاء المعالجة
            if self.processing:
                self.status.set("✅ تم الانتهاء من تنظيف جميع الملفات بنجاح!")
                messagebox.showinfo("مكتمل",
                    f"تم تنظيف {total_files} ملف بنجاح!\n"
                    f"السجلات المعالجة: {self.stats['total_records_processed']:,}\n"
                    f"المكررات المحذوفة: {self.stats['duplicates_removed']:,}")
            else:
                self.status.set("❌ تم إيقاف العملية")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء المعالجة:\n{str(e)}")
            self.status.set("❌ حدث خطأ أثناء المعالجة")
        finally:
            # إعادة تفعيل الأزرار
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_csv_file(self, input_file, output_path):
        """معالجة ملف CSV واحد"""
        try:
            # قراءة الملف مع معالجة الترميز
            data_rows = []
            encoding = self.detect_encoding(input_file)

            with open(input_file, 'r', encoding=encoding, errors='ignore') as f:
                # محاولة تحديد الفاصل
                sample = f.read(1024)
                f.seek(0)
                delimiter = self.detect_delimiter(sample)

                csv_reader = csv.reader(f, delimiter=delimiter)

                # قراءة البيانات
                for row_num, row in enumerate(csv_reader):
                    if not self.processing:
                        return

                    # تنظيف البيانات
                    if self.clean_data.get():
                        row = self.clean_row_data(row)

                    # توحيد الرؤوس (للسطر الأول فقط)
                    if row_num == 0 and self.standardize_headers.get():
                        row = self.standardize_row_headers(row)

                    data_rows.append(row)

                    # تحديث عداد السجلات
                    if row_num % 10000 == 0:
                        self.processed_records.set(self.processed_records.get() + 10000)
                        self.stats['total_records_processed'] += 10000

            # إزالة المكررات
            if self.remove_duplicates.get() and len(data_rows) > 1:
                original_count = len(data_rows)
                data_rows = self.remove_duplicate_rows(data_rows)
                duplicates_removed = original_count - len(data_rows)
                self.stats['duplicates_removed'] += duplicates_removed

            # تقسيم الملفات الكبيرة أو حفظ ملف واحد
            if self.split_large_files.get() and len(data_rows) > self.max_rows_per_file.get():
                self.split_and_save_file(data_rows, input_file.stem, output_path)
            else:
                self.save_cleaned_file(data_rows, input_file.stem, output_path)

        except Exception as e:
            self.stats['errors_fixed'] += 1
            print(f"خطأ في معالجة الملف {input_file.name}: {str(e)}")

    def detect_encoding(self, file_path):
        """تحديد ترميز الملف"""
        encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)  # قراءة عينة
                return encoding
            except UnicodeDecodeError:
                continue

        return 'utf-8'  # افتراضي

    def detect_delimiter(self, sample):
        """تحديد الفاصل المستخدم في CSV"""
        delimiters = [',', ';', '\t', '|']
        delimiter_counts = {}

        for delimiter in delimiters:
            delimiter_counts[delimiter] = sample.count(delimiter)

        # إرجاع الفاصل الأكثر استخداماً
        return max(delimiter_counts, key=delimiter_counts.get) if max(delimiter_counts.values()) > 0 else ','

    def clean_row_data(self, row):
        """تنظيف بيانات السطر"""
        cleaned_row = []
        for cell in row:
            if isinstance(cell, str):
                # إزالة المسافات الزائدة
                cell = cell.strip()

                # إزالة الرموز الغريبة والتحكم
                cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', cell)

                # تنظيف علامات الاقتباس المكررة
                cell = re.sub(r'"+', '"', cell)
                cell = cell.strip('"')

                # إصلاح الترميز الشائع
                if self.fix_encoding.get():
                    cell = self.fix_common_encoding_issues(cell)

            cleaned_row.append(cell)

        return cleaned_row

    def fix_common_encoding_issues(self, text):
        """إصلاح مشاكل الترميز الشائعة"""
        # قاموس لإصلاح الأحرف المشوهة
        fixes = {
            'Ã¡': 'á', 'Ã©': 'é', 'Ã­': 'í', 'Ã³': 'ó', 'Ãº': 'ú',
            'Ã±': 'ñ', 'Ã¼': 'ü', 'Ã¤': 'ä', 'Ã¶': 'ö',
            'â€™': "'", 'â€œ': '"', 'â€': '"', 'â€¦': '...',
            'Ø§': 'ا', 'Ø¨': 'ب', 'Øª': 'ت', 'Ø«': 'ث'
        }

        for wrong, correct in fixes.items():
            text = text.replace(wrong, correct)

        return text

    def standardize_row_headers(self, headers):
        """توحيد رؤوس الأعمدة"""
        # قاموس لتوحيد أسماء الأعمدة
        header_mapping = {
            'id': 'facebook_id',
            'fb_id': 'facebook_id',
            'facebook id': 'facebook_id',
            'user_id': 'facebook_id',
            'name': 'first_name',
            'firstname': 'first_name',
            'first name': 'first_name',
            'fname': 'first_name',
            'lastname': 'last_name',
            'last name': 'last_name',
            'lname': 'last_name',
            'surname': 'last_name',
            'mail': 'email',
            'e-mail': 'email',
            'email_address': 'email',
            'telephone': 'phone',
            'mobile': 'phone',
            'cell': 'phone',
            'phone_number': 'phone',
            'sex': 'gender',
            'bday': 'birthday',
            'birth_date': 'birthday',
            'dob': 'birthday',
            'birth_year': 'birthday_year',
            'year': 'birthday_year',
            'city': 'location',
            'address': 'location',
            'place': 'location',
            'home': 'hometown',
            'home_town': 'hometown',
            'origin': 'hometown',
            'nation': 'country',
            'nationality': 'country',
            'lang': 'locale',
            'language': 'locale',
            'user': 'username',
            'handle': 'username',
            'url': 'profile_url',
            'link': 'profile_url',
            'profile': 'profile_url',
            'school': 'education',
            'university': 'education',
            'college': 'education',
            'job': 'work',
            'occupation': 'work',
            'company': 'work',
            'employer': 'work',
            'faith': 'religion',
            'belief': 'religion',
            'status': 'relationship',
            'marital': 'relationship',
            'relationship_status': 'relationship',
            'bio': 'about_me',
            'description': 'about_me',
            'about': 'about_me',
            'summary': 'about_me',
            'hobbies': 'interests',
            'likes': 'interests'
        }

        standardized_headers = []
        for header in headers:
            if isinstance(header, str):
                # تنظيف وتوحيد الرأس
                clean_header = header.lower().strip().replace(' ', '_').replace('-', '_')
                clean_header = re.sub(r'[^\w_]', '', clean_header)

                # البحث عن مطابقة في القاموس
                if clean_header in header_mapping:
                    standardized_headers.append(header_mapping[clean_header])
                elif clean_header in self.standard_headers:
                    standardized_headers.append(clean_header)
                else:
                    # الاحتفاظ بالرأس الأصلي إذا لم يتم العثور على مطابقة
                    standardized_headers.append(clean_header)
            else:
                standardized_headers.append(str(header))

        return standardized_headers

    def remove_duplicate_rows(self, data_rows):
        """إزالة السجلات المكررة"""
        if len(data_rows) <= 1:
            return data_rows

        # الاحتفاظ بالرأس
        headers = data_rows[0]
        data_only = data_rows[1:]

        # تحويل السجلات إلى tuples للمقارنة
        seen = set()
        unique_rows = [headers]  # بدء بالرؤوس

        for row in data_only:
            # إنشاء مفتاح فريد للسطر
            row_key = tuple(str(cell).strip().lower() for cell in row)

            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)

        return unique_rows

    def split_and_save_file(self, data_rows, base_filename, output_path):
        """تقسيم وحفظ الملفات الكبيرة"""
        if len(data_rows) <= 1:
            return

        headers = data_rows[0]
        data_only = data_rows[1:]
        max_rows = self.max_rows_per_file.get()

        # حساب عدد الملفات المطلوبة
        num_files = (len(data_only) + max_rows - 1) // max_rows

        for i in range(num_files):
            start_idx = i * max_rows
            end_idx = min((i + 1) * max_rows, len(data_only))

            # إنشاء اسم الملف الجديد
            if num_files > 1:
                filename = f"{base_filename}_part_{i+1:03d}.csv"
            else:
                filename = f"{base_filename}_cleaned.csv"

            # حفظ الجزء
            file_data = [headers] + data_only[start_idx:end_idx]
            self.save_cleaned_file(file_data, filename.replace('.csv', ''), output_path)

        self.stats['total_output_files'] += num_files

    def save_cleaned_file(self, data_rows, filename, output_path):
        """حفظ الملف المنظف"""
        output_file = output_path / f"{filename}_cleaned.csv"

        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_MINIMAL)
                writer.writerows(data_rows)

            self.stats['total_output_files'] += 1

        except Exception as e:
            print(f"خطأ في حفظ الملف {output_file}: {str(e)}")
            self.stats['errors_fixed'] += 1

    def update_stats_display(self):
        """تحديث عرض الإحصائيات"""
        self.files_label.config(text=f"{self.processed_files.get()} / {self.total_files.get()}")
        self.records_label.config(text=f"{self.stats['total_records_processed']:,}")
        self.duplicates_label.config(text=f"{self.stats['duplicates_removed']:,}")
        self.errors_label.config(text=f"{self.stats['errors_fixed']:,}")

    def open_output_folder(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())
        else:
            messagebox.showinfo("معلومات", "مجلد الإخراج غير محدد أو غير موجود")

    def clear_output(self):
        """مسح مجلد النتائج"""
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "لم يتم تحديد مجلد الإخراج")
            return

        if messagebox.askyesno("تأكيد", "هل تريد حذف جميع الملفات في مجلد الإخراج؟"):
            try:
                output_path = Path(self.output_folder.get())
                for file in output_path.glob("*_cleaned.csv"):
                    file.unlink()
                messagebox.showinfo("تم", "تم حذف جميع الملفات المنظفة")
                self.status.set("تم مسح مجلد النتائج")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الملفات:\n{str(e)}")


def main():
    """تشغيل التطبيق الرئيسي"""
    root = tk.Tk()

    # تحسين مظهر التطبيق
    try:
        # محاولة استخدام نمط حديث
        style = ttk.Style()
        style.theme_use('clam')

        # تخصيص الألوان
        style.configure('Accent.TButton',
                       background='#0078d4',
                       foreground='white',
                       font=('Arial', 10, 'bold'))

    except Exception:
        pass  # استخدام النمط الافتراضي إذا فشل التخصيص

    # إنشاء التطبيق
    app = CSVCleanerApp(root)

    # تشغيل التطبيق
    root.mainloop()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV
أداة متخصصة لتنظيف وتحسين ملفات CSV الكبيرة

المميزات:
- تنظيف البيانات وإزالة الرموز الغريبة
- توحيد رؤوس الأعمدة
- إزالة السجلات المكررة
- تقسيم الملفات الكبيرة
- إصلاح مشاكل الترميز
"""

import os
import csv
import sys
import time
import shutil
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class CSVCleanerApp:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV")
        self.root.geometry("1000x700")
        self.root.configure(pady=10, padx=10)
        
        # المتغيرات الأساسية
        self.init_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # رؤوس الأعمدة الموحدة للبيانات الاجتماعية
        self.standard_headers = [
            'facebook_id', 'first_name', 'last_name', 'email', 'phone',
            'gender', 'birthday', 'birthday_year', 'location', 'hometown',
            'country', 'locale', 'username', 'profile_url', 'education',
            'work', 'religion', 'relationship', 'about_me', 'interests'
        ]
        
    def init_variables(self):
        """تهيئة المتغيرات"""
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processed_files = tk.IntVar()
        self.total_files = tk.IntVar()
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.fix_encoding = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        self.split_large_files = tk.BooleanVar(value=True)
        self.max_rows_per_file = tk.IntVar(value=1000000)
        
        # إحصائيات
        self.stats = {
            'total_records_processed': 0,
            'duplicates_removed': 0,
            'errors_fixed': 0
        }

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', pady=(0, 15))
        
        title_label = tk.Label(
            title_frame,
            text="🧹 CSV Cleaner Tool",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="أداة تنظيف وتحسين ملفات CSV للبيانات الاجتماعية",
            font=('Arial', 12),
            fg='#7f8c8d'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # قسم اختيار المجلدات
        self.create_folder_selection()
        
        # قسم خيارات التنظيف
        self.create_cleaning_options()
        
        # قسم التحكم والتقدم
        self.create_control_section()

    def create_folder_selection(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Label(input_frame, text="📂 مجلد ملفات CSV المراد تنظيفها:").pack(anchor='w')
        
        input_entry_frame = ttk.Frame(input_frame)
        input_entry_frame.pack(fill='x', pady=2)
        ttk.Entry(input_entry_frame, textvariable=self.input_folder, font=('Arial', 10)).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_entry_frame, text="تصفح", command=self.browse_input_folder).pack(side='right')
        
        # مجلد الإخراج
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Label(output_frame, text="💾 مجلد حفظ الملفات المنظفة:").pack(anchor='w')
        
        output_entry_frame = ttk.Frame(output_frame)
        output_entry_frame.pack(fill='x', pady=2)
        ttk.Entry(output_entry_frame, textvariable=self.output_folder, font=('Arial', 10)).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_entry_frame, text="تصفح", command=self.browse_output_folder).pack(side='right')

    def create_cleaning_options(self):
        """إنشاء قسم خيارات التنظيف"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        ttk.Checkbutton(left_column, text="🔧 توحيد رؤوس الأعمدة", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🧹 تنظيف البيانات", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🔤 إصلاح الترميز", 
                       variable=self.fix_encoding).pack(anchor='w', pady=2)
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        ttk.Checkbutton(right_column, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)
        ttk.Checkbutton(right_column, text="✂️ تقسيم الملفات الكبيرة", 
                       variable=self.split_large_files).pack(anchor='w', pady=2)
        
        # إعداد حجم التقسيم
        split_frame = ttk.Frame(right_column)
        split_frame.pack(anchor='w', pady=2)
        ttk.Label(split_frame, text="📊 سجلات لكل ملف:").pack(side='left')
        ttk.Entry(split_frame, textvariable=self.max_rows_per_file, width=10).pack(side='left', padx=(5, 0))

    def create_control_section(self):
        """إنشاء قسم التحكم والتقدم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='both', expand=True)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning, style='Accent.TButton')
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_output_folder).pack(side='left', padx=(0, 10))
        
        # شريط التقدم
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(progress_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=2)
        
        # معلومات الحالة
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill='x')
        
        ttk.Label(status_frame, text="📄 الملف الحالي:").pack(anchor='w')
        ttk.Label(status_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w', pady=(0, 5))
        
        ttk.Label(status_frame, text="ℹ️ الحالة:").pack(anchor='w')
        ttk.Label(status_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_input_folder(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            self.total_files.set(len(csv_files))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")

    def browse_output_folder(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد حفظ النتائج")
        if folder:
            self.output_folder.set(folder)
            self.status.set("تم تحديد مجلد الإخراج")

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        # تحديث حالة الأزرار
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        # بدء المعالجة
        self.processing = True
        Thread(target=self.cleaning_process, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_process(self):
        """عملية التنظيف الرئيسية"""
        try:
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            if not csv_files:
                messagebox.showwarning("تنبيه", "لا توجد ملفات CSV في المجلد")
                return
            
            total_files = len(csv_files)
            
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break
                    
                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                
                # معالجة الملف
                self.process_single_file(csv_file)
                
                # تحديث التقدم
                progress = ((i + 1) / total_files) * 100
                self.progress.set(progress)
                self.root.update_idletasks()
            
            if self.processing:
                self.status.set("✅ تم الانتهاء من تنظيف جميع الملفات!")
                messagebox.showinfo("مكتمل", "تم تنظيف جميع الملفات بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_single_file(self, input_file):
        """معالجة ملف CSV واحد"""
        try:
            # قراءة الملف
            data_rows = []
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                csv_reader = csv.reader(f)
                for row in csv_reader:
                    if self.clean_data.get():
                        row = [cell.strip() for cell in row]
                    data_rows.append(row)
            
            # توحيد الرؤوس
            if self.standardize_headers.get() and data_rows:
                data_rows[0] = self.standardize_row_headers(data_rows[0])
            
            # إزالة المكررات
            if self.remove_duplicates.get():
                data_rows = self.remove_duplicate_rows(data_rows)
            
            # حفظ الملف المنظف
            output_file = Path(self.output_folder.get()) / f"{input_file.stem}_cleaned.csv"
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(data_rows)
                
        except Exception as e:
            print(f"خطأ في معالجة {input_file.name}: {str(e)}")

    def standardize_row_headers(self, headers):
        """توحيد رؤوس الأعمدة"""
        header_mapping = {
            'id': 'facebook_id', 'fb_id': 'facebook_id', 'user_id': 'facebook_id',
            'name': 'first_name', 'firstname': 'first_name', 'fname': 'first_name',
            'lastname': 'last_name', 'lname': 'last_name', 'surname': 'last_name',
            'mail': 'email', 'e-mail': 'email', 'email_address': 'email',
            'telephone': 'phone', 'mobile': 'phone', 'phone_number': 'phone',
            'sex': 'gender', 'bday': 'birthday', 'birth_date': 'birthday'
        }
        
        standardized = []
        for header in headers:
            clean_header = str(header).lower().strip().replace(' ', '_')
            standardized.append(header_mapping.get(clean_header, clean_header))
        
        return standardized

    def remove_duplicate_rows(self, data_rows):
        """إزالة السجلات المكررة"""
        if len(data_rows) <= 1:
            return data_rows
            
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def open_output_folder(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())
        else:
            messagebox.showinfo("معلومات", "مجلد الإخراج غير محدد")


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    
    # تحسين المظهر
    try:
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Accent.TButton', 
                       background='#3498db', 
                       foreground='white',
                       font=('Arial', 10, 'bold'))
    except Exception:
        pass
    
    app = CSVCleanerApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()

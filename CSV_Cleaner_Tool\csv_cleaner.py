#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV
أداة متخصصة لتنظيف وتحسين ملفات CSV الكبيرة

المميزات:
- تنظيف البيانات وإزالة الرموز الغريبة
- توحيد رؤوس الأعمدة
- إزالة السجلات المكررة
- تقسيم الملفات الكبيرة
- إصلاح مشاكل الترميز
"""

import os
import csv
import sys
import time
import shutil
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class CSVCleanerApp:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV")
        self.root.geometry("1000x700")
        self.root.configure(pady=10, padx=10)
        
        # المتغيرات الأساسية
        self.init_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # رؤوس الأعمدة الموحدة للبيانات الاجتماعية
        self.standard_headers = [
            'facebook_id', 'first_name', 'last_name', 'email', 'phone',
            'gender', 'birthday', 'birthday_year', 'location', 'hometown',
            'country', 'locale', 'username', 'profile_url', 'education',
            'work', 'religion', 'relationship', 'about_me', 'interests'
        ]
        
    def init_variables(self):
        """تهيئة المتغيرات"""
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processed_files = tk.IntVar()
        self.total_files = tk.IntVar()
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.fix_encoding = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        self.split_large_files = tk.BooleanVar(value=True)
        self.max_rows_per_file = tk.IntVar(value=1000000)
        
        # إحصائيات
        self.stats = {
            'total_records_processed': 0,
            'duplicates_removed': 0,
            'errors_fixed': 0
        }

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', pady=(0, 15))
        
        title_label = tk.Label(
            title_frame,
            text="🧹 CSV Cleaner Tool",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="أداة تنظيف وتحسين ملفات CSV للبيانات الاجتماعية",
            font=('Arial', 12),
            fg='#7f8c8d'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # قسم اختيار المجلدات
        self.create_folder_selection()
        
        # قسم خيارات التنظيف
        self.create_cleaning_options()

        # قسم معاينة البيانات
        self.create_preview_section()

        # قسم التحكم والتقدم
        self.create_control_section()

    def create_folder_selection(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Label(input_frame, text="📂 مجلد ملفات CSV المراد تنظيفها:").pack(anchor='w')
        
        input_entry_frame = ttk.Frame(input_frame)
        input_entry_frame.pack(fill='x', pady=2)
        ttk.Entry(input_entry_frame, textvariable=self.input_folder, font=('Arial', 10)).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_entry_frame, text="تصفح", command=self.browse_input_folder).pack(side='right')
        
        # مجلد الإخراج
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Label(output_frame, text="💾 مجلد حفظ الملفات المنظفة:").pack(anchor='w')
        
        output_entry_frame = ttk.Frame(output_frame)
        output_entry_frame.pack(fill='x', pady=2)
        ttk.Entry(output_entry_frame, textvariable=self.output_folder, font=('Arial', 10)).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_entry_frame, text="تصفح", command=self.browse_output_folder).pack(side='right')

    def create_cleaning_options(self):
        """إنشاء قسم خيارات التنظيف"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        ttk.Checkbutton(left_column, text="🔧 توحيد رؤوس الأعمدة", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🧹 تنظيف البيانات", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🔤 إصلاح الترميز", 
                       variable=self.fix_encoding).pack(anchor='w', pady=2)
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        ttk.Checkbutton(right_column, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)
        ttk.Checkbutton(right_column, text="✂️ تقسيم الملفات الكبيرة", 
                       variable=self.split_large_files).pack(anchor='w', pady=2)
        
        # إعداد حجم التقسيم
        split_frame = ttk.Frame(right_column)
        split_frame.pack(anchor='w', pady=2)
        ttk.Label(split_frame, text="📊 سجلات لكل ملف:").pack(side='left')
        ttk.Entry(split_frame, textvariable=self.max_rows_per_file, width=10).pack(side='left', padx=(5, 0))

    def create_control_section(self):
        """إنشاء قسم التحكم والتقدم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='both', expand=True)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning, style='Accent.TButton')
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_output_folder).pack(side='left', padx=(0, 10))
        
        # شريط التقدم
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(progress_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=2)
        
        # معلومات الحالة
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill='x')
        
        ttk.Label(status_frame, text="📄 الملف الحالي:").pack(anchor='w')
        ttk.Label(status_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w', pady=(0, 5))
        
        ttk.Label(status_frame, text="ℹ️ الحالة:").pack(anchor='w')
        ttk.Label(status_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_input_folder(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            self.total_files.set(len(csv_files))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")

    def browse_output_folder(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد حفظ النتائج")
        if folder:
            self.output_folder.set(folder)
            self.status.set("تم تحديد مجلد الإخراج")

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        # تحديث حالة الأزرار
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        # بدء المعالجة
        self.processing = True
        Thread(target=self.cleaning_process, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_process(self):
        """عملية التنظيف الرئيسية"""
        try:
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            if not csv_files:
                messagebox.showwarning("تنبيه", "لا توجد ملفات CSV في المجلد")
                return
            
            total_files = len(csv_files)
            
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break
                    
                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                
                # معالجة الملف
                self.process_single_file(csv_file)
                
                # تحديث التقدم
                progress = ((i + 1) / total_files) * 100
                self.progress.set(progress)
                self.root.update_idletasks()
            
            if self.processing:
                self.status.set("✅ تم الانتهاء من تنظيف جميع الملفات!")
                messagebox.showinfo("مكتمل", "تم تنظيف جميع الملفات بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_single_file(self, input_file):
        """معالجة ملف CSV واحد مع تحسينات متقدمة"""
        try:
            # كشف الترميز المناسب
            encoding = self.detect_encoding(input_file)

            # كشف الفاصل المستخدم
            delimiter = self.detect_delimiter(input_file, encoding)

            # قراءة الملف مع الترميز والفاصل الصحيح
            data_rows = []
            with open(input_file, 'r', encoding=encoding, errors='ignore') as f:
                csv_reader = csv.reader(f, delimiter=delimiter)

                for row_num, row in enumerate(csv_reader):
                    if not self.processing:
                        break

                    # تنظيف البيانات
                    if self.clean_data.get():
                        row = self.clean_row_data(row)

                    # توحيد الرؤوس (للسطر الأول فقط)
                    if row_num == 0 and self.standardize_headers.get():
                        row = self.standardize_row_headers(row)

                    data_rows.append(row)

            # إزالة المكررات
            if self.remove_duplicates.get():
                original_count = len(data_rows)
                data_rows = self.remove_duplicate_rows(data_rows)
                removed_count = original_count - len(data_rows)
                print(f"تم حذف {removed_count} سجل مكرر من {input_file.name}")

            # تقسيم الملفات الكبيرة أو حفظ ملف واحد
            if self.split_large_files.get() and len(data_rows) > self.max_rows_per_file.get():
                self.split_and_save_file(data_rows, input_file.stem)
            else:
                self.save_cleaned_file(data_rows, input_file.stem)

        except Exception as e:
            print(f"خطأ في معالجة {input_file.name}: {str(e)}")
            self.status.set(f"خطأ في معالجة {input_file.name}")

    def detect_encoding(self, file_path):
        """كشف ترميز الملف"""
        encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1', 'windows-1256']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)  # قراءة عينة
                return encoding
            except (UnicodeDecodeError, UnicodeError):
                continue

        return 'utf-8'  # افتراضي

    def detect_delimiter(self, file_path, encoding):
        """كشف الفاصل المستخدم في CSV"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                sample = f.read(2048)  # قراءة عينة أكبر

            # اختبار الفواصل المختلفة
            delimiters = [',', ';', '\t', '|', ':']
            delimiter_counts = {}

            for delimiter in delimiters:
                delimiter_counts[delimiter] = sample.count(delimiter)

            # إرجاع الفاصل الأكثر استخداماً
            best_delimiter = max(delimiter_counts, key=delimiter_counts.get)

            # التأكد من أن الفاصل منطقي
            if delimiter_counts[best_delimiter] > 0:
                return best_delimiter
            else:
                return ','  # افتراضي

        except Exception:
            return ','  # افتراضي

    def clean_row_data(self, row):
        """تنظيف بيانات السطر"""
        cleaned_row = []
        for cell in row:
            if isinstance(cell, str):
                # إزالة المسافات الزائدة
                cell = cell.strip()

                # إزالة علامات الاقتباس المكررة
                cell = cell.strip('"').strip("'")

                # إزالة الرموز الغريبة
                cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', cell)

                # تنظيف علامات الاقتباس المكررة
                cell = re.sub(r'"+', '"', cell)

                # إصلاح الترميز إذا كان مطلوباً
                if self.fix_encoding.get():
                    cell = self.fix_common_encoding_issues(cell)

            cleaned_row.append(cell)

        return cleaned_row

    def fix_common_encoding_issues(self, text):
        """إصلاح مشاكل الترميز الشائعة"""
        # قاموس لإصلاح الأحرف المشوهة
        fixes = {
            'Ã¡': 'á', 'Ã©': 'é', 'Ã­': 'í', 'Ã³': 'ó', 'Ãº': 'ú',
            'Ã±': 'ñ', 'Ã¼': 'ü', 'Ã¤': 'ä', 'Ã¶': 'ö',
            'â€™': "'", 'â€œ': '"', 'â€': '"', 'â€¦': '...',
            'Ø§': 'ا', 'Ø¨': 'ب', 'Øª': 'ت', 'Ø«': 'ث'
        }

        for wrong, correct in fixes.items():
            text = text.replace(wrong, correct)

        return text

    def split_and_save_file(self, data_rows, base_filename):
        """تقسيم وحفظ الملفات الكبيرة"""
        if len(data_rows) <= 1:
            return

        headers = data_rows[0]
        data_only = data_rows[1:]
        max_rows = self.max_rows_per_file.get()

        # حساب عدد الملفات المطلوبة
        num_files = (len(data_only) + max_rows - 1) // max_rows

        for i in range(num_files):
            start_idx = i * max_rows
            end_idx = min((i + 1) * max_rows, len(data_only))

            # إنشاء اسم الملف الجديد
            if num_files > 1:
                filename = f"{base_filename}_part_{i+1:03d}_cleaned"
            else:
                filename = f"{base_filename}_cleaned"

            # حفظ الجزء
            file_data = [headers] + data_only[start_idx:end_idx]
            self.save_cleaned_file(file_data, filename)

        print(f"تم تقسيم {base_filename} إلى {num_files} ملف")

    def save_cleaned_file(self, data_rows, filename):
        """حفظ الملف المنظف"""
        output_file = Path(self.output_folder.get()) / f"{filename}.csv"

        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_MINIMAL)
                writer.writerows(data_rows)

            print(f"تم حفظ: {output_file.name}")

        except Exception as e:
            print(f"خطأ في حفظ الملف {output_file}: {str(e)}")

    def standardize_row_headers(self, headers):
        """توحيد رؤوس الأعمدة مع دعم شامل للأسماء المختلفة"""
        # قاموس شامل لتوحيد أسماء الأعمدة
        header_mapping = {
            # معرفات Facebook
            'id': 'facebook_id',
            'fb_id': 'facebook_id',
            'facebook id': 'facebook_id',
            'user_id': 'facebook_id',
            'userid': 'facebook_id',
            'fbid': 'facebook_id',

            # الأسماء
            'name': 'first_name',
            'firstname': 'first_name',
            'first name': 'first_name',
            'fname': 'first_name',
            'given_name': 'first_name',
            'اسم': 'first_name',
            'الاسم الأول': 'first_name',

            'lastname': 'last_name',
            'last name': 'last_name',
            'lname': 'last_name',
            'surname': 'last_name',
            'family_name': 'last_name',
            'اسم العائلة': 'last_name',
            'الاسم الأخير': 'last_name',

            # البريد الإلكتروني
            'mail': 'email',
            'e-mail': 'email',
            'email_address': 'email',
            'emailaddress': 'email',
            'بريد إلكتروني': 'email',
            'ايميل': 'email',

            # الهاتف
            'telephone': 'phone',
            'mobile': 'phone',
            'cell': 'phone',
            'phone_number': 'phone',
            'phonenumber': 'phone',
            'tel': 'phone',
            'هاتف': 'phone',
            'رقم الهاتف': 'phone',
            'موبايل': 'phone',

            # الجنس
            'sex': 'gender',
            'جنس': 'gender',
            'الجنس': 'gender',

            # تاريخ الميلاد
            'bday': 'birthday',
            'birth_date': 'birthday',
            'birthdate': 'birthday',
            'date_of_birth': 'birthday',
            'dob': 'birthday',
            'تاريخ الميلاد': 'birthday',
            'ميلاد': 'birthday',

            'birth_year': 'birthday_year',
            'year': 'birthday_year',
            'سنة الميلاد': 'birthday_year',

            # الموقع
            'city': 'location',
            'address': 'location',
            'place': 'location',
            'موقع': 'location',
            'مدينة': 'location',
            'عنوان': 'location',

            'home': 'hometown',
            'home_town': 'hometown',
            'hometown': 'hometown',
            'origin': 'hometown',
            'المدينة الأصلية': 'hometown',

            'nation': 'country',
            'nationality': 'country',
            'بلد': 'country',
            'دولة': 'country',

            # اللغة والمنطقة
            'lang': 'locale',
            'language': 'locale',
            'لغة': 'locale',

            # اسم المستخدم والرابط
            'user': 'username',
            'handle': 'username',
            'اسم المستخدم': 'username',

            'url': 'profile_url',
            'link': 'profile_url',
            'profile': 'profile_url',
            'رابط': 'profile_url',

            # التعليم والعمل
            'school': 'education',
            'university': 'education',
            'college': 'education',
            'تعليم': 'education',
            'جامعة': 'education',
            'مدرسة': 'education',

            'job': 'work',
            'occupation': 'work',
            'company': 'work',
            'employer': 'work',
            'عمل': 'work',
            'وظيفة': 'work',
            'شركة': 'work',

            # الديانة والحالة الاجتماعية
            'faith': 'religion',
            'belief': 'religion',
            'ديانة': 'religion',
            'دين': 'religion',

            'status': 'relationship',
            'marital': 'relationship',
            'relationship_status': 'relationship',
            'حالة اجتماعية': 'relationship',
            'زواج': 'relationship',

            # نبذة شخصية واهتمامات
            'bio': 'about_me',
            'description': 'about_me',
            'about': 'about_me',
            'summary': 'about_me',
            'نبذة': 'about_me',
            'وصف': 'about_me',

            'hobbies': 'interests',
            'likes': 'interests',
            'اهتمامات': 'interests',
            'هوايات': 'interests'
        }

        standardized_headers = []
        for header in headers:
            if isinstance(header, str):
                # تنظيف وتوحيد الرأس
                clean_header = header.lower().strip()
                clean_header = clean_header.replace(' ', '_').replace('-', '_')
                clean_header = re.sub(r'[^\w_\u0600-\u06FF]', '', clean_header)  # دعم العربية

                # البحث عن مطابقة في القاموس
                if clean_header in header_mapping:
                    standardized_headers.append(header_mapping[clean_header])
                elif clean_header in self.standard_headers:
                    standardized_headers.append(clean_header)
                else:
                    # الاحتفاظ بالرأس الأصلي مع تنظيفه
                    standardized_headers.append(clean_header if clean_header else f'column_{len(standardized_headers)}')
            else:
                standardized_headers.append(f'column_{len(standardized_headers)}')

        return standardized_headers

    def remove_duplicate_rows(self, data_rows):
        """إزالة السجلات المكررة"""
        if len(data_rows) <= 1:
            return data_rows
            
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def open_output_folder(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())
        else:
            messagebox.showinfo("معلومات", "مجلد الإخراج غير محدد")


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    
    # تحسين المظهر
    try:
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Accent.TButton', 
                       background='#3498db', 
                       foreground='white',
                       font=('Arial', 10, 'bold'))
    except Exception:
        pass
    
    app = CSVCleanerApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()

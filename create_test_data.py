#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء بيانات تجريبية كبيرة لاختبار شريط التقدم
"""

import csv
import random
from faker import Faker

def create_large_test_data():
    """إنشاء ملف بيانات كبير للاختبار"""
    
    # إنشاء مولد بيانات وهمية
    fake = Faker(['ar_SA', 'en_US'])  # العربية والإنجليزية
    
    # أسماء عربية
    arabic_first_names = ['أحمد', 'محمد', 'فاطمة', 'عائشة', 'علي', 'حسن', 'مريم', 'نور', 'يوسف', 'سارة']
    arabic_last_names = ['محمد', 'أحمد', 'علي', 'حسن', 'إبراهيم', 'عبدالله', 'خالد', 'سعد', 'فؤاد', 'حسام']
    
    # مواقع عربية
    arabic_locations = ['القاهرة', 'الإسكندرية', 'الجيزة', 'المعادي', 'مدينة نصر', 'الزمالك', 'حلوان', 'شبرا']
    
    # وظائف عربية
    arabic_jobs = ['مهندس', 'طبيب', 'مدرس', 'محاسب', 'محامي', 'صيدلي', 'مبرمج', 'مصمم']
    
    # جامعات عربية
    arabic_universities = ['جامعة القاهرة', 'جامعة الإسكندرية', 'عين شمس', 'الجامعة الأمريكية', 'جامعة حلوان']
    
    # حالات اجتماعية
    relationship_status = ['متزوج', 'أعزب', 'مطلق', 'أرمل']
    
    # إنشاء الملف
    filename = 'large_test_data.csv'
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # كتابة رأس الجدول
        writer.writerow([
            'facebook_id', 'first_name', 'last_name', 'email', 'phone', 
            'location', 'age', 'gender', 'relationship', 'work', 'education', 'interests'
        ])
        
        # إنشاء 50,000 سجل للاختبار
        for i in range(50000):
            # اختيار عشوائي بين العربية والإنجليزية
            use_arabic = random.choice([True, False])
            
            if use_arabic:
                first_name = random.choice(arabic_first_names)
                last_name = random.choice(arabic_last_names)
                location = random.choice(arabic_locations)
                work = random.choice(arabic_jobs)
                education = random.choice(arabic_universities)
                gender = random.choice(['ذكر', 'أنثى'])
                relationship = random.choice(relationship_status)
            else:
                first_name = fake.first_name()
                last_name = fake.last_name()
                location = fake.city()
                work = fake.job()
                education = fake.company()
                gender = random.choice(['Male', 'Female'])
                relationship = random.choice(['Married', 'Single', 'Divorced'])
            
            # بيانات مشتركة
            facebook_id = f"10000{1000000000 + i}"
            email = f"{first_name.lower()}.{last_name.lower()}@{random.choice(['gmail.com', 'yahoo.com', 'hotmail.com'])}"
            phone = f"+20{random.randint(1000000000, 1999999999)}"
            age = random.randint(18, 65)
            interests = random.choice(['كرة القدم', 'قراءة', 'موسيقى', 'سفر', 'طبخ', 'رياضة'])
            
            writer.writerow([
                facebook_id, first_name, last_name, email, phone,
                location, age, gender, relationship, work, education, interests
            ])
            
            # طباعة التقدم كل 5000 سجل
            if (i + 1) % 5000 == 0:
                print(f"تم إنشاء {i + 1:,} سجل...")
    
    print(f"✅ تم إنشاء ملف {filename} بنجاح مع 50,000 سجل!")
    print(f"📁 حجم الملف: {round(os.path.getsize(filename) / (1024*1024), 2)} MB")

if __name__ == "__main__":
    import os
    try:
        create_large_test_data()
    except ImportError:
        print("❌ يرجى تثبيت مكتبة faker أولاً:")
        print("pip install faker")
    except Exception as e:
        print(f"❌ خطأ: {e}")

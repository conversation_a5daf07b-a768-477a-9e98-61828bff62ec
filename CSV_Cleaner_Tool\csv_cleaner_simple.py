#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - نسخة مبسطة وسريعة
أداة تنظيف ملفات CSV مع نسق ثابت للبيانات الاجتماعية
"""

import os
import csv
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class SimpleCSVCleaner:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV")
        self.root.geometry("900x600")
        self.root.configure(pady=10, padx=10)
        
        # النسق الثابت للبيانات الاجتماعية
        self.standard_headers = [
            'facebook_id', 'first_name', 'last_name', 'email', 'phone',
            'birthday', 'birthday_year', 'locale', 'hometown', 'work',
            'country', 'education', 'relationship', 'religion', 'about_me',
            'gender', 'location'
        ]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processing = False
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        
        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 CSV Cleaner Tool",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()

    def create_folder_section(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد ملفات CSV:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Checkbutton(options_frame, text="🔧 توحيد رؤوس الأعمدة (17 عمود ثابت)", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(options_frame, text="🧹 تنظيف البيانات", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(options_frame, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)

    def create_control_section(self):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='both', expand=True)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # معلومات الحالة
        ttk.Label(control_frame, text="📄 الملف الحالي:").pack(anchor='w', pady=(10, 0))
        ttk.Label(control_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w')
        
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            if not csv_files:
                messagebox.showwarning("تنبيه", "لا توجد ملفات CSV")
                return
            
            total_files = len(csv_files)
            
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break
                
                # تحديث الواجهة
                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                self.progress.set((i / total_files) * 100)
                self.root.update_idletasks()
                
                # معالجة الملف
                self.process_file(csv_file)
                
                # تحديث التقدم
                self.progress.set(((i + 1) / total_files) * 100)
                self.root.update_idletasks()
            
            if self.processing:
                self.status.set("✅ تم الانتهاء من تنظيف جميع الملفات!")
                messagebox.showinfo("مكتمل", f"تم تنظيف {total_files} ملف بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_file(self, input_file):
        """معالجة ملف CSV واحد"""
        try:
            # قراءة الملف
            data_rows = []
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                # محاولة كشف الفاصل
                sample = f.read(1024)
                f.seek(0)
                delimiter = ',' if sample.count(',') > sample.count(';') else ';'
                
                csv_reader = csv.reader(f, delimiter=delimiter)
                data_rows = list(csv_reader)
            
            if not data_rows:
                return
            
            # تنظيف البيانات
            if self.clean_data.get():
                data_rows = self.clean_data_rows(data_rows)
            
            # توحيد الرؤوس
            if self.standardize_headers.get():
                data_rows = self.apply_standard_format(data_rows)
            
            # إزالة المكررات
            if self.remove_duplicates.get():
                data_rows = self.remove_duplicate_rows(data_rows)
            
            # حفظ الملف المنظف
            output_file = Path(self.output_folder.get()) / f"{input_file.stem}_cleaned.csv"
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(data_rows)
                
        except Exception as e:
            print(f"خطأ في معالجة {input_file.name}: {str(e)}")

    def clean_data_rows(self, data_rows):
        """تنظيف البيانات"""
        cleaned_rows = []
        for row in data_rows:
            cleaned_row = []
            for cell in row:
                if isinstance(cell, str):
                    # إزالة المسافات والاقتباس
                    cell = cell.strip().strip('"').strip("'")
                    # إزالة الرموز الغريبة
                    cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cell)
                cleaned_row.append(cell)
            cleaned_rows.append(cleaned_row)
        return cleaned_rows

    def apply_standard_format(self, data_rows):
        """تطبيق النسق الثابت"""
        if not data_rows:
            return [self.standard_headers]
        
        # إنشاء ملف جديد بالنسق الثابت
        standardized_data = [self.standard_headers]
        
        # تحويل رؤوس الأعمدة الأصلية
        original_headers = [str(h).lower().strip() for h in data_rows[0]]
        
        # قاموس تحويل الرؤوس
        header_mapping = {
            'id': 0, 'fb_id': 0, 'facebook_id': 0,
            'name': 1, 'first_name': 1, 'firstname': 1,
            'last_name': 2, 'lastname': 2, 'surname': 2,
            'email': 3, 'mail': 3, 'e-mail': 3,
            'phone': 4, 'tel': 4, 'telephone': 4, 'mobile': 4,
            'birthday': 5, 'birth_date': 5, 'bday': 5,
            'birthday_year': 6, 'birth_year': 6, 'year': 6,
            'locale': 7, 'language': 7, 'lang': 7,
            'hometown': 8, 'home_town': 8, 'origin': 8,
            'work': 9, 'job': 9, 'occupation': 9,
            'country': 10, 'nation': 10,
            'education': 11, 'school': 11, 'university': 11,
            'relationship': 12, 'status': 12, 'marital': 12,
            'religion': 13, 'faith': 13,
            'about_me': 14, 'bio': 14, 'about': 14,
            'gender': 15, 'sex': 15,
            'location': 16, 'city': 16, 'address': 16
        }
        
        # معالجة كل صف من البيانات
        for row in data_rows[1:]:
            new_row = [''] * len(self.standard_headers)
            
            # تطبيق البيانات حسب التحويل
            for i, cell in enumerate(row):
                if i < len(original_headers):
                    header = original_headers[i]
                    target_index = header_mapping.get(header, -1)
                    if target_index >= 0 and target_index < len(new_row):
                        new_row[target_index] = cell
            
            standardized_data.append(new_row)
        
        return standardized_data

    def remove_duplicate_rows(self, data_rows):
        """إزالة المكررات"""
        if len(data_rows) <= 1:
            return data_rows
        
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = SimpleCSVCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

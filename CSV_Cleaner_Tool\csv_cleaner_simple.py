#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - نسخة مبسطة وسريعة
أداة تنظيف ملفات CSV مع نسق ثابت للبيانات الاجتماعية
"""

import os
import csv
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class SimpleCSVCleaner:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV")
        self.root.geometry("900x600")
        self.root.configure(pady=10, padx=10)
        
        # النسق الثابت الجديد (19 عمود بدون blank)
        self.standard_headers = [
            'facebook_id',      # 0 - معرف Facebook
            'email',            # 1 - البريد الإلكتروني
            'phone',            # 2 - رقم الهاتف
            'religion',         # 3 - الديانة
            'birthday_year',    # 4 - سنة الميلاد
            'first_name',       # 5 - الاسم الأول
            'last_name',        # 6 - اسم العائلة
            'gender',           # 7 - الجنس
            'link',             # 8 - الرابط
            'username',         # 9 - اسم المستخدم
            'fullname',         # 10 - الاسم الكامل
            'beo',              # 11 - beo
            'company',          # 12 - الشركة
            'title',            # 13 - المسمى الوظيفي
            'hometown',         # 14 - المدينة الأصلية
            'country',          # 15 - البلد
            'education',        # 16 - التعليم
            'user',             # 17 - المستخدم
            'status'            # 18 - الحالة
        ]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processing = False
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        
        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 CSV Cleaner Tool",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()

    def create_folder_section(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد ملفات CSV:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Checkbutton(options_frame, text="🔧 توحيد رؤوس الأعمدة (17 عمود ثابت)", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(options_frame, text="🧹 تنظيف البيانات", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(options_frame, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)

    def create_control_section(self):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='both', expand=True)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # معلومات الحالة
        ttk.Label(control_frame, text="📄 الملف الحالي:").pack(anchor='w', pady=(10, 0))
        ttk.Label(control_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w')
        
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            if not csv_files:
                messagebox.showwarning("تنبيه", "لا توجد ملفات CSV")
                return
            
            total_files = len(csv_files)
            
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break
                
                # تحديث الواجهة
                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                self.progress.set((i / total_files) * 100)
                self.root.update_idletasks()
                
                # معالجة الملف
                self.process_file(csv_file)
                
                # تحديث التقدم
                self.progress.set(((i + 1) / total_files) * 100)
                self.root.update_idletasks()
            
            if self.processing:
                self.status.set("✅ تم الانتهاء من تنظيف جميع الملفات!")
                messagebox.showinfo("مكتمل", f"تم تنظيف {total_files} ملف بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def detect_file_encoding(self, file_path):
        """كشف ترميز الملف بدقة"""
        encodings_to_try = [
            'utf-8-sig',  # UTF-8 with BOM
            'utf-8',      # UTF-8
            'cp1256',     # Windows Arabic
            'iso-8859-6', # Arabic ISO
            'windows-1256', # Windows Arabic
            'latin1',     # Latin-1
            'cp1252',     # Windows Western
        ]

        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    # قراءة عينة للتأكد من صحة الترميز
                    sample = f.read(1024)
                    # التحقق من وجود نصوص عربية صحيحة
                    if any('\u0600' <= char <= '\u06FF' for char in sample):
                        return encoding
                    elif encoding in ['utf-8', 'utf-8-sig'] and sample:
                        return encoding
                return encoding  # إرجاع الترميز إذا نجحت القراءة
            except (UnicodeDecodeError, UnicodeError):
                continue

        return 'utf-8'  # افتراضي

    def process_file(self, input_file):
        """معالجة ملف CSV واحد"""
        try:
            # قراءة الملف مع كشف الفاصل الصحيح
            data_rows = []
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                # قراءة عينة لكشف الفاصل
                sample = f.read(2048)
                f.seek(0)

                # كشف الفاصل الأكثر استخداماً
                delimiters = [',', ';', '\t', '|']
                delimiter_counts = {d: sample.count(d) for d in delimiters}
                delimiter = max(delimiter_counts, key=delimiter_counts.get)

                # إذا لم يتم العثور على فاصل واضح، استخدم الفاصلة
                if delimiter_counts[delimiter] == 0:
                    delimiter = ','

                # قراءة البيانات مع الفاصل الصحيح
                csv_reader = csv.reader(f, delimiter=delimiter, quotechar='"', skipinitialspace=True)
                data_rows = list(csv_reader)
            
            if not data_rows:
                return
            
            # تنظيف البيانات
            if self.clean_data.get():
                data_rows = self.clean_data_rows(data_rows)
            
            # توحيد الرؤوس
            if self.standardize_headers.get():
                data_rows = self.apply_standard_format(data_rows)
            
            # إزالة المكررات
            if self.remove_duplicates.get():
                data_rows = self.remove_duplicate_rows(data_rows)
            
            # التحقق من وجود بيانات فعلية (أكثر من رؤوس الأعمدة)
            if len(data_rows) <= 1:
                print(f"تحذير: الملف {input_file.name} لا يحتوي على بيانات (رؤوس أعمدة فقط)")
                return

            # حفظ الملف المنظف (ملف واحد فقط)
            output_file = Path(self.output_folder.get()) / f"{input_file.stem}_cleaned.csv"

            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                # استخدام فاصلة كفاصل ثابت وبدون اقتباس إلا عند الضرورة
                writer = csv.writer(f, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
                writer.writerows(data_rows)

            print(f"تم حفظ: {output_file.name} - {len(data_rows)-1} سجل")
                
        except Exception as e:
            print(f"خطأ في معالجة {input_file.name}: {str(e)}")

    def clean_data_rows(self, data_rows):
        """تنظيف البيانات مع حماية النصوص العربية"""
        cleaned_rows = []
        for row in data_rows:
            cleaned_row = []
            for cell in row:
                if isinstance(cell, str):
                    # إزالة المسافات الزائدة
                    cell = cell.strip()

                    # إزالة جميع علامات الاقتباس (مهم جداً!)
                    cell = cell.replace('"', '').replace("'", '')

                    # إزالة الرموز الغريبة مع حماية النصوص العربية
                    # إزالة رموز التحكم فقط، مع الحفاظ على العربية (\u0600-\u06FF)
                    cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cell)

                    # تنظيف المسافات المتعددة
                    cell = re.sub(r'\s+', ' ', cell).strip()

                    # إصلاح مشاكل الترميز الشائعة للعربية
                    cell = self.fix_arabic_encoding(cell)

                cleaned_row.append(cell if cell else '')  # تأكد من عدم وجود None
            cleaned_rows.append(cleaned_row)
        return cleaned_rows

    def fix_arabic_encoding(self, text):
        """إصلاح مشاكل ترميز النصوص العربية"""
        if not text:
            return text

        # قاموس إصلاح الأحرف العربية المشوهة
        arabic_fixes = {
            # مشاكل ترميز شائعة
            'Ø§': 'ا',  # ألف
            'Ø¨': 'ب',  # باء
            'Øª': 'ت',  # تاء
            'Ø«': 'ث',  # ثاء
            'Ø¬': 'ج',  # جيم
            'Ø­': 'ح',  # حاء
            'Ø®': 'خ',  # خاء
            'Ø¯': 'د',  # دال
            'Ø°': 'ذ',  # ذال
            'Ø±': 'ر',  # راء
            'Ø²': 'ز',  # زاي
            'Ø³': 'س',  # سين
            'Ø´': 'ش',  # شين
            'Øµ': 'ص',  # صاد
            'Ø¶': 'ض',  # ضاد
            'Ø·': 'ط',  # طاء
            'Ø¸': 'ظ',  # ظاء
            'Ø¹': 'ع',  # عين
            'Øº': 'غ',  # غين
            'Ù': 'ف',   # فاء
            'Ù‚': 'ق',  # قاف
            'Ùƒ': 'ك',  # كاف
            'Ù„': 'ل',  # لام
            'Ù…': 'م',  # ميم
            'Ù†': 'ن',  # نون
            'Ù‡': 'ه',  # هاء
            'Ùˆ': 'و',  # واو
            'ÙŠ': 'ي',  # ياء
            'Ø©': 'ة',  # تاء مربوطة
            'Ø¡': 'ء',  # همزة
            'Ø¢': 'آ',  # ألف مد
            'Ø£': 'أ',  # همزة على ألف
            'Ø¤': 'ؤ',  # همزة على واو
            'Ø¥': 'إ',  # همزة تحت ألف
            'Ø¦': 'ئ',  # همزة على ياء

            # مشاكل ترميز أخرى
            'ï±': '',   # رموز غريبة
            'â€': '',   # رموز غريبة
            'Ã': '',    # رموز غريبة
        }

        # تطبيق الإصلاحات
        for wrong, correct in arabic_fixes.items():
            text = text.replace(wrong, correct)

        return text

    def apply_standard_format(self, data_rows):
        """تطبيق النسق الثابت الصحيح (25 عمود)"""
        if not data_rows:
            return [self.standard_headers]

        # إنشاء ملف جديد بالنسق الثابت
        standardized_data = [self.standard_headers]

        # تحويل رؤوس الأعمدة الأصلية
        original_headers = [str(h).lower().strip().replace('"', '').replace("'", '') for h in data_rows[0]]

        # قاموس تحويل الرؤوس للنسق الجديد (19 عمود بدون blank)
        header_mapping = {
            # facebook_id - العمود 0
            'id': 0, 'fb_id': 0, 'facebook_id': 0, 'user_id': 0,

            # email - العمود 1
            'email': 1, 'mail': 1, 'e-mail': 1, 'e_mail': 1,

            # phone - العمود 2
            'phone': 2, 'tel': 2, 'telephone': 2, 'mobile': 2,

            # religion - العمود 3
            'religion': 3, 'faith': 3,

            # birthday_year - العمود 4
            'birthday_year': 4, 'birth_year': 4, 'year': 4, 'age': 4,

            # first_name - العمود 5
            'first_name': 5, 'firstname': 5, 'fname': 5,

            # last_name - العمود 6
            'last_name': 6, 'lastname': 6, 'surname': 6, 'lname': 6,

            # gender - العمود 7
            'gender': 7, 'sex': 7,

            # link - العمود 8
            'link': 8, 'url': 8, 'profile_url': 8, 'profile': 8,

            # username - العمود 9
            'username': 9, 'user': 9, 'handle': 9,

            # fullname - العمود 10
            'fullname': 10, 'full_name': 10, 'name': 10, 'display_name': 10,

            # beo - العمود 11
            'beo': 11,

            # company - العمود 12
            'company': 12, 'work': 12, 'job': 12, 'employer': 12, 'occupation': 12,

            # title - العمود 13
            'title': 13, 'position': 13, 'job_title': 13,

            # hometown - العمود 14
            'hometown': 14, 'home_town': 14, 'origin': 14,

            # country - العمود 15
            'country': 15, 'nation': 15,

            # education - العمود 16
            'education': 16, 'school': 16, 'university': 16, 'college': 16,

            # user - العمود 17
            'user': 17,

            # status - العمود 18
            'status': 18, 'relationship': 18, 'marital': 18, 'relationship_status': 18
        }

        # معالجة كل صف من البيانات
        for row in data_rows[1:]:
            # إنشاء صف جديد بـ 19 عمود (كلها فارغة في البداية)
            new_row = [''] * len(self.standard_headers)

            # تطبيق البيانات حسب التحويل
            for i, cell in enumerate(row):
                if i < len(original_headers):
                    header = original_headers[i]
                    target_index = header_mapping.get(header, -1)

                    if target_index >= 0 and target_index < len(new_row):
                        # تنظيف الخلية من علامات الاقتباس
                        clean_cell = str(cell).replace('"', '').replace("'", '').strip()
                        new_row[target_index] = clean_cell
                    else:
                        # إذا لم يتم العثور على تطابق، ضع البيانات في أول موضع فارغ
                        for j in range(len(new_row)):
                            if not new_row[j]:
                                clean_cell = str(cell).replace('"', '').replace("'", '').strip()
                                new_row[j] = clean_cell
                                break

            # التأكد من وجود بيانات في الصف
            if any(cell.strip() for cell in new_row):
                standardized_data.append(new_row)

        return standardized_data

    def remove_duplicate_rows(self, data_rows):
        """إزالة المكررات"""
        if len(data_rows) <= 1:
            return data_rows
        
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = SimpleCSVCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

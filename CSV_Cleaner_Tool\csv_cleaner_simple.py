#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - نسخة مبسطة وسريعة
أداة تنظيف ملفات CSV مع نسق ثابت للبيانات الاجتماعية
"""

import os
import csv
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class SimpleCSVCleaner:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV")
        self.root.geometry("900x600")
        self.root.configure(pady=10, padx=10)
        
        # النسق الثابت الصحيح (25 عمود حسب طلب المستخدم)
        self.standard_headers = [
            'facebook_id',      # 0 - معرف Facebook
            'blank',            # 1 - عمود فارغ
            'email',            # 2 - البريد الإلكتروني
            'phone',            # 3 - رقم الهاتف
            'religion',         # 4 - الديانة
            'birthday_year',    # 5 - سنة الميلاد
            'first_name',       # 6 - الاسم الأول
            'last_name',        # 7 - اسم العائلة
            'gender',           # 8 - الجنس
            'link',             # 9 - الرابط
            'blank',            # 10 - عمود فارغ
            'username',         # 11 - اسم المستخدم
            'fullname',         # 12 - الاسم الكامل
            'beo',              # 13 - beo
            'company',          # 14 - الشركة
            'title',            # 15 - المسمى الوظيفي
            'hometown',         # 16 - المدينة الأصلية
            'country',          # 17 - البلد
            'education',        # 18 - التعليم
            'user',             # 19 - المستخدم
            'blank',            # 20 - عمود فارغ
            'blank',            # 21 - عمود فارغ
            'blank',            # 22 - عمود فارغ
            'status',           # 23 - الحالة
            'blank'             # 24 - عمود فارغ
        ]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processing = False
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        
        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 CSV Cleaner Tool",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()

    def create_folder_section(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد ملفات CSV:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Checkbutton(options_frame, text="🔧 توحيد رؤوس الأعمدة (17 عمود ثابت)", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(options_frame, text="🧹 تنظيف البيانات", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(options_frame, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)

    def create_control_section(self):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='both', expand=True)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # معلومات الحالة
        ttk.Label(control_frame, text="📄 الملف الحالي:").pack(anchor='w', pady=(10, 0))
        ttk.Label(control_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w')
        
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            if not csv_files:
                messagebox.showwarning("تنبيه", "لا توجد ملفات CSV")
                return
            
            total_files = len(csv_files)
            
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break
                
                # تحديث الواجهة
                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                self.progress.set((i / total_files) * 100)
                self.root.update_idletasks()
                
                # معالجة الملف
                self.process_file(csv_file)
                
                # تحديث التقدم
                self.progress.set(((i + 1) / total_files) * 100)
                self.root.update_idletasks()
            
            if self.processing:
                self.status.set("✅ تم الانتهاء من تنظيف جميع الملفات!")
                messagebox.showinfo("مكتمل", f"تم تنظيف {total_files} ملف بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def detect_file_encoding(self, file_path):
        """كشف ترميز الملف بدقة"""
        encodings_to_try = [
            'utf-8-sig',  # UTF-8 with BOM
            'utf-8',      # UTF-8
            'cp1256',     # Windows Arabic
            'iso-8859-6', # Arabic ISO
            'windows-1256', # Windows Arabic
            'latin1',     # Latin-1
            'cp1252',     # Windows Western
        ]

        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    # قراءة عينة للتأكد من صحة الترميز
                    sample = f.read(1024)
                    # التحقق من وجود نصوص عربية صحيحة
                    if any('\u0600' <= char <= '\u06FF' for char in sample):
                        return encoding
                    elif encoding in ['utf-8', 'utf-8-sig'] and sample:
                        return encoding
                return encoding  # إرجاع الترميز إذا نجحت القراءة
            except (UnicodeDecodeError, UnicodeError):
                continue

        return 'utf-8'  # افتراضي

    def process_file(self, input_file):
        """معالجة ملف CSV واحد مع دعم كامل للعربية"""
        try:
            # كشف الترميز الصحيح
            encoding = self.detect_file_encoding(input_file)

            # قراءة الملف مع كشف الفاصل الصحيح
            data_rows = []
            with open(input_file, 'r', encoding=encoding, errors='replace') as f:
                # قراءة عينة لكشف الفاصل
                sample = f.read(2048)
                f.seek(0)

                # كشف الفاصل الأكثر استخداماً
                delimiters = [',', ';', '\t', '|']
                delimiter_counts = {d: sample.count(d) for d in delimiters}
                delimiter = max(delimiter_counts, key=delimiter_counts.get)

                # إذا لم يتم العثور على فاصل واضح، استخدم الفاصلة
                if delimiter_counts[delimiter] == 0:
                    delimiter = ','

                # قراءة البيانات مع الفاصل الصحيح
                csv_reader = csv.reader(f, delimiter=delimiter, quotechar='"', skipinitialspace=True)
                data_rows = list(csv_reader)
            
            if not data_rows:
                return
            
            # تنظيف البيانات
            if self.clean_data.get():
                data_rows = self.clean_data_rows(data_rows)
            
            # توحيد الرؤوس
            if self.standardize_headers.get():
                data_rows = self.apply_standard_format(data_rows)
            
            # إزالة المكررات
            if self.remove_duplicates.get():
                data_rows = self.remove_duplicate_rows(data_rows)
            
            # حفظ الملف المنظف مع دعم كامل للعربية
            output_file = Path(self.output_folder.get()) / f"{input_file.stem}_cleaned.csv"

            # حفظ بترميز UTF-8 مع BOM لضمان ظهور العربية في Excel
            with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
                # استخدام فاصلة كفاصل ثابت وبدون اقتباس إلا عند الضرورة
                writer = csv.writer(f, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
                writer.writerows(data_rows)

            # إنشاء نسخة إضافية بترميز UTF-8 عادي
            output_file_utf8 = Path(self.output_folder.get()) / f"{input_file.stem}_cleaned_utf8.csv"
            with open(output_file_utf8, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
                writer.writerows(data_rows)
                
        except Exception as e:
            print(f"خطأ في معالجة {input_file.name}: {str(e)}")

    def clean_data_rows(self, data_rows):
        """تنظيف البيانات مع حماية النصوص العربية"""
        cleaned_rows = []
        for row in data_rows:
            cleaned_row = []
            for cell in row:
                if isinstance(cell, str):
                    # إزالة المسافات الزائدة
                    cell = cell.strip()

                    # إزالة جميع علامات الاقتباس (مهم جداً!)
                    cell = cell.replace('"', '').replace("'", '')

                    # إزالة الرموز الغريبة مع حماية النصوص العربية
                    # إزالة رموز التحكم فقط، مع الحفاظ على العربية (\u0600-\u06FF)
                    cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cell)

                    # تنظيف المسافات المتعددة
                    cell = re.sub(r'\s+', ' ', cell).strip()

                    # إصلاح مشاكل الترميز الشائعة للعربية
                    cell = self.fix_arabic_encoding(cell)

                cleaned_row.append(cell if cell else '')  # تأكد من عدم وجود None
            cleaned_rows.append(cleaned_row)
        return cleaned_rows

    def fix_arabic_encoding(self, text):
        """إصلاح مشاكل ترميز النصوص العربية"""
        if not text:
            return text

        # قاموس إصلاح الأحرف العربية المشوهة
        arabic_fixes = {
            # مشاكل ترميز شائعة
            'Ø§': 'ا',  # ألف
            'Ø¨': 'ب',  # باء
            'Øª': 'ت',  # تاء
            'Ø«': 'ث',  # ثاء
            'Ø¬': 'ج',  # جيم
            'Ø­': 'ح',  # حاء
            'Ø®': 'خ',  # خاء
            'Ø¯': 'د',  # دال
            'Ø°': 'ذ',  # ذال
            'Ø±': 'ر',  # راء
            'Ø²': 'ز',  # زاي
            'Ø³': 'س',  # سين
            'Ø´': 'ش',  # شين
            'Øµ': 'ص',  # صاد
            'Ø¶': 'ض',  # ضاد
            'Ø·': 'ط',  # طاء
            'Ø¸': 'ظ',  # ظاء
            'Ø¹': 'ع',  # عين
            'Øº': 'غ',  # غين
            'Ù': 'ف',   # فاء
            'Ù‚': 'ق',  # قاف
            'Ùƒ': 'ك',  # كاف
            'Ù„': 'ل',  # لام
            'Ù…': 'م',  # ميم
            'Ù†': 'ن',  # نون
            'Ù‡': 'ه',  # هاء
            'Ùˆ': 'و',  # واو
            'ÙŠ': 'ي',  # ياء
            'Ø©': 'ة',  # تاء مربوطة
            'Ø¡': 'ء',  # همزة
            'Ø¢': 'آ',  # ألف مد
            'Ø£': 'أ',  # همزة على ألف
            'Ø¤': 'ؤ',  # همزة على واو
            'Ø¥': 'إ',  # همزة تحت ألف
            'Ø¦': 'ئ',  # همزة على ياء

            # مشاكل ترميز أخرى
            'ï±': '',   # رموز غريبة
            'â€': '',   # رموز غريبة
            'Ã': '',    # رموز غريبة
        }

        # تطبيق الإصلاحات
        for wrong, correct in arabic_fixes.items():
            text = text.replace(wrong, correct)

        return text

    def apply_standard_format(self, data_rows):
        """تطبيق النسق الثابت الصحيح (25 عمود)"""
        if not data_rows:
            return [self.standard_headers]

        # إنشاء ملف جديد بالنسق الثابت
        standardized_data = [self.standard_headers]

        # تحويل رؤوس الأعمدة الأصلية
        original_headers = [str(h).lower().strip().replace('"', '').replace("'", '') for h in data_rows[0]]

        # قاموس تحويل الرؤوس للنسق الجديد (25 عمود)
        header_mapping = {
            # facebook_id - العمود 0
            'id': 0, 'fb_id': 0, 'facebook_id': 0, 'user_id': 0,

            # email - العمود 2
            'email': 2, 'mail': 2, 'e-mail': 2, 'e_mail': 2,

            # phone - العمود 3
            'phone': 3, 'tel': 3, 'telephone': 3, 'mobile': 3,

            # religion - العمود 4
            'religion': 4, 'faith': 4,

            # birthday_year - العمود 5
            'birthday_year': 5, 'birth_year': 5, 'year': 5, 'age': 5,

            # first_name - العمود 6
            'first_name': 6, 'firstname': 6, 'fname': 6,

            # last_name - العمود 7
            'last_name': 7, 'lastname': 7, 'surname': 7, 'lname': 7,

            # gender - العمود 8
            'gender': 8, 'sex': 8,

            # link - العمود 9
            'link': 9, 'url': 9, 'profile_url': 9, 'profile': 9,

            # username - العمود 11
            'username': 11, 'user': 11, 'handle': 11,

            # fullname - العمود 12
            'fullname': 12, 'full_name': 12, 'name': 12, 'display_name': 12,

            # company - العمود 14
            'company': 14, 'work': 14, 'job': 14, 'employer': 14, 'occupation': 14,

            # title - العمود 15
            'title': 15, 'position': 15, 'job_title': 15,

            # hometown - العمود 16
            'hometown': 16, 'home_town': 16, 'origin': 16,

            # country - العمود 17
            'country': 17, 'nation': 17,

            # education - العمود 18
            'education': 18, 'school': 18, 'university': 18, 'college': 18,

            # status - العمود 23
            'status': 23, 'relationship': 23, 'marital': 23, 'relationship_status': 23
        }

        # معالجة كل صف من البيانات
        for row in data_rows[1:]:
            # إنشاء صف جديد بـ 25 عمود (كلها فارغة في البداية)
            new_row = [''] * len(self.standard_headers)

            # تطبيق البيانات حسب التحويل
            for i, cell in enumerate(row):
                if i < len(original_headers):
                    header = original_headers[i]
                    target_index = header_mapping.get(header, -1)

                    if target_index >= 0 and target_index < len(new_row):
                        # تنظيف الخلية من علامات الاقتباس
                        clean_cell = str(cell).replace('"', '').replace("'", '').strip()
                        new_row[target_index] = clean_cell

            # التأكد من أن الأعمدة المحددة كـ "blank" تبقى فارغة
            for idx, header in enumerate(self.standard_headers):
                if header == 'blank':
                    new_row[idx] = ''

            standardized_data.append(new_row)

        return standardized_data

    def remove_duplicate_rows(self, data_rows):
        """إزالة المكررات"""
        if len(data_rows) <= 1:
            return data_rows
        
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = SimpleCSVCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

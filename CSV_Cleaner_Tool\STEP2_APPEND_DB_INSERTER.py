#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗄️ المرحلة الثانية: إضافة البيانات النظيفة لقاعدة البيانات الموجودة
STEP 2: Append Clean Data to Existing Database
"""

import os
import csv
import sqlite3
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
from pathlib import Path
import time

class AppendDatabaseInserter:
    def __init__(self, root):
        """تهيئة أداة الإدراج مع الإضافة"""
        self.root = root
        self.root.title("🗄️ المرحلة الثانية: إضافة البيانات لقاعدة البيانات الموجودة")
        self.root.geometry("800x650")
        self.root.configure(pady=10, padx=10)
        
        # الفاصل البديل
        self.PIPE_DELIMITER = "|"
        
        # اسم قاعدة البيانات الثابت
        self.DB_NAME = "fulldata.db"
        
        # النسق النهائي الصحيح (19 عمود بعد حذف الأعمدة x)
        self.HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown',
            'country', 'education', 'user', 'status'
        ]
        
        # المتغيرات
        self.clean_data_folder = tk.StringVar()
        self.db_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.processing = False
        
        # خيارات الإدراج
        self.file_format = tk.StringVar(value="txt")  # txt أو csv
        self.batch_size = tk.IntVar(value=1000)  # حجم الدفعة
        self.create_indexes = tk.BooleanVar(value=True)
        self.append_mode = tk.BooleanVar(value=True)  # وضع الإضافة
        
        self.setup_ui()

    def setup_ui(self):
        """إعداد الواجهة"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🗄️ المرحلة الثانية: إضافة البيانات لقاعدة البيانات",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # معلومات
        info_frame = ttk.LabelFrame(self.root, text="ℹ️ معلومات", padding=10)
        info_frame.pack(fill='x', pady=(0, 10))
        
        info_text = f"قاعدة البيانات: {self.DB_NAME}\nالفاصل: {self.PIPE_DELIMITER}\nوضع الإضافة: البيانات الجديدة تُضاف للملف الموجود"
        ttk.Label(info_frame, text=info_text).pack(anchor='w')
        
        # المجلدات
        self.create_folder_section()
        
        # الخيارات
        self.create_options_section()
        
        # التحكم
        self.create_control_section()
        
        # النتائج
        self.create_results_section()

    def create_folder_section(self):
        """قسم المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد البيانات النظيفة
        ttk.Label(folder_frame, text="📂 مجلد البيانات النظيفة الجديدة:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.clean_data_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_clean_data).pack(side='right')
        
        # مجلد قاعدة البيانات
        ttk.Label(folder_frame, text="🗄️ مجلد قاعدة البيانات الموجودة:").pack(anchor='w', pady=(10, 0))
        db_frame = ttk.Frame(folder_frame)
        db_frame.pack(fill='x', pady=5)
        ttk.Entry(db_frame, textvariable=self.db_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(db_frame, text="تصفح", command=self.browse_db_folder).pack(side='right')

    def create_options_section(self):
        """قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات الإدراج", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True)
        
        # نوع الملفات
        ttk.Label(left_column, text="📄 نوع الملفات النظيفة:").pack(anchor='w')
        format_frame = ttk.Frame(left_column)
        format_frame.pack(fill='x', pady=5)
        
        ttk.Radiobutton(format_frame, text="📝 TXT مع |", 
                       variable=self.file_format, value="txt").pack(side='left', padx=(0, 20))
        ttk.Radiobutton(format_frame, text="📈 CSV مع |", 
                       variable=self.file_format, value="csv").pack(side='left')
        
        # حجم الدفعة
        ttk.Label(left_column, text="📦 حجم الدفعة:").pack(anchor='w', pady=(10, 0))
        batch_frame = ttk.Frame(left_column)
        batch_frame.pack(fill='x', pady=5)
        
        ttk.Entry(batch_frame, textvariable=self.batch_size, width=10).pack(side='left')
        ttk.Label(batch_frame, text="سجل في الدفعة الواحدة").pack(side='left', padx=(5, 0))
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        ttk.Checkbutton(right_column, text="➕ وضع الإضافة (إضافة للموجود)", 
                       variable=self.append_mode).pack(anchor='w', pady=2)
        ttk.Checkbutton(right_column, text="🔍 إنشاء فهارس للبحث السريع", 
                       variable=self.create_indexes).pack(anchor='w', pady=2)

    def create_control_section(self):
        """قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="➕ إضافة البيانات الجديدة", 
                                      command=self.start_insertion)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_insertion, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📊 فحص قاعدة البيانات", 
                  command=self.check_database).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # الحالة
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def create_results_section(self):
        """قسم النتائج"""
        results_frame = ttk.LabelFrame(self.root, text="📊 نتائج الإدراج", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log(self, message):
        """إضافة رسالة للنتائج"""
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_clean_data(self):
        """تصفح مجلد البيانات النظيفة"""
        folder = filedialog.askdirectory(title="اختر مجلد البيانات النظيفة الجديدة")
        if folder:
            self.clean_data_folder.set(folder)
            files = list(Path(folder).glob("*_cleaned.*"))
            self.status.set(f"تم اختيار المجلد - وجد {len(files)} ملف نظيف جديد")

    def browse_db_folder(self):
        """تصفح مجلد قاعدة البيانات"""
        folder = filedialog.askdirectory(title="اختر مجلد قاعدة البيانات الموجودة")
        if folder:
            self.db_folder.set(folder)
            db_path = Path(folder) / self.DB_NAME
            if db_path.exists():
                self.status.set(f"تم العثور على قاعدة البيانات الموجودة: {self.DB_NAME}")
            else:
                self.status.set(f"سيتم إنشاء قاعدة بيانات جديدة: {self.DB_NAME}")

    def start_insertion(self):
        """بدء الإدراج"""
        if not self.clean_data_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد البيانات النظيفة")
            return
            
        if not self.db_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد قاعدة البيانات")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.insertion_thread, daemon=True).start()

    def stop_insertion(self):
        """إيقاف الإدراج"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def insertion_thread(self):
        """معالجة الإدراج"""
        try:
            self.log("🗄️ بدء إضافة البيانات الجديدة لقاعدة البيانات...")
            self.log(f"📁 قاعدة البيانات: {self.DB_NAME}")
            
            # مسار قاعدة البيانات
            db_path = Path(self.db_folder.get()) / self.DB_NAME
            
            # فحص قاعدة البيانات الموجودة
            if db_path.exists():
                existing_count = self.get_existing_records_count(db_path)
                self.log(f"📊 السجلات الموجودة: {existing_count:,}")
            else:
                self.log(f"🆕 إنشاء قاعدة بيانات جديدة...")
                existing_count = 0
            
            # إنشاء أو تحديث قاعدة البيانات
            self.ensure_database_exists(db_path)
            
            # جمع الملفات النظيفة
            clean_folder = Path(self.clean_data_folder.get())
            file_pattern = f"*_cleaned.{self.file_format.get()}"
            files = list(clean_folder.glob(file_pattern))
            
            if not files:
                self.log(f"❌ لا توجد ملفات {self.file_format.get().upper()} نظيفة")
                return
            
            self.log(f"📊 عدد الملفات النظيفة الجديدة: {len(files)}")
            
            total_new_records = 0
            
            for i, file_path in enumerate(files):
                if not self.processing:
                    break
                
                self.log(f"\n📄 إضافة الملف {i+1}/{len(files)}: {file_path.name}")
                self.status.set(f"إضافة {file_path.name}")
                self.progress.set((i / len(files)) * 100)
                
                records = self.append_file_data(file_path, db_path)
                total_new_records += records
                
                self.root.update_idletasks()
            
            if self.processing:
                # إنشاء الفهارس
                if self.create_indexes.get():
                    self.log(f"\n🔍 تحديث فهارس البحث السريع...")
                    self.create_database_indexes(db_path)
                
                # إحصائيات نهائية
                final_count = self.get_existing_records_count(db_path)
                
                self.log(f"\n🎉 تم الانتهاء من الإضافة!")
                self.log(f"📊 السجلات المضافة الجديدة: {total_new_records:,}")
                self.log(f"📊 إجمالي السجلات في قاعدة البيانات: {final_count:,}")
                self.log(f"🗄️ قاعدة البيانات: {db_path}")
                self.status.set("✅ تم الانتهاء من الإضافة!")
                messagebox.showinfo("مكتمل", f"تم إضافة {total_new_records:,} سجل جديد!\nإجمالي السجلات: {final_count:,}")
            
        except Exception as e:
            self.log(f"❌ خطأ عام: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def get_existing_records_count(self, db_path):
        """الحصول على عدد السجلات الموجودة"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM cleaned_data")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0

    def ensure_database_exists(self, db_path):
        """التأكد من وجود قاعدة البيانات"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # إنشاء الجدول إذا لم يكن موجوداً
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS cleaned_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                {', '.join([f'{header} TEXT' for header in self.HEADERS])}
            )
            """
            cursor.execute(create_table_sql)
            
            conn.commit()
            conn.close()
            
            self.log(f"🗄️ قاعدة البيانات جاهزة: {db_path.name}")
            
        except Exception as e:
            self.log(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}")
            raise

    def append_file_data(self, file_path, db_path):
        """إضافة بيانات ملف واحد"""
        try:
            # قراءة البيانات
            if self.file_format.get() == "txt":
                data = self.read_pipe_txt_file(file_path)
            else:
                data = self.read_pipe_csv_file(file_path)
            
            if not data:
                self.log(f"⚠️ لا توجد بيانات في الملف")
                return 0
            
            self.log(f"📋 قراءة {len(data)} سجل")
            
            # إضافة البيانات بدفعات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            placeholders = ', '.join(['?' for _ in self.HEADERS])
            insert_sql = f"INSERT INTO cleaned_data ({', '.join(self.HEADERS)}) VALUES ({placeholders})"
            
            batch_size = self.batch_size.get()
            inserted_count = 0
            
            for i in range(0, len(data), batch_size):
                if not self.processing:
                    break
                
                batch = data[i:i + batch_size]
                cursor.executemany(insert_sql, batch)
                inserted_count += len(batch)
                
                # تحديث التقدم
                progress = (inserted_count / len(data)) * 100
                self.log(f"📦 تم إضافة {inserted_count}/{len(data)} سجل ({progress:.1f}%)")
            
            conn.commit()
            conn.close()
            
            self.log(f"✅ تم إضافة {inserted_count} سجل من {file_path.name}")
            return inserted_count
            
        except Exception as e:
            self.log(f"❌ خطأ في إضافة {file_path.name}: {str(e)}")
            return 0

    def read_pipe_txt_file(self, file_path):
        """قراءة ملف TXT بالفاصل |"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            data = []
            for line in lines[1:]:  # تخطي الرؤوس
                line = line.strip()
                if line:
                    # تقسيم بالفاصل |
                    parts = [part.strip() for part in line.split(self.PIPE_DELIMITER)]
                    
                    # توحيد مع النسق (19 عمود)
                    row = [''] * len(self.HEADERS)
                    for i, part in enumerate(parts):
                        if i < len(row):
                            # التأكد من عدم وجود فواصل عادية
                            clean_part = part.replace(',', '').strip()
                            row[i] = clean_part
                    
                    # إضافة السجل إذا كان يحتوي على بيانات
                    if any(cell.strip() for cell in row):
                        data.append(row)
            
            return data
            
        except Exception as e:
            self.log(f"❌ خطأ في قراءة ملف TXT: {str(e)}")
            return []

    def read_pipe_csv_file(self, file_path):
        """قراءة ملف CSV بالفاصل |"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter='|')
                data = list(reader)[1:]  # تخطي الرؤوس
            
            # توحيد مع النسق (19 عمود)
            standardized_data = []
            for row in data:
                standardized_row = [''] * len(self.HEADERS)
                for i, cell in enumerate(row):
                    if i < len(standardized_row):
                        # التأكد من عدم وجود فواصل عادية
                        clean_cell = str(cell).replace(',', '').strip()
                        standardized_row[i] = clean_cell
                
                # إضافة السجل إذا كان يحتوي على بيانات
                if any(cell.strip() for cell in standardized_row):
                    standardized_data.append(standardized_row)
            
            return standardized_data
            
        except Exception as e:
            self.log(f"❌ خطأ في قراءة ملف CSV: {str(e)}")
            return []

    def create_database_indexes(self, db_path):
        """إنشاء فهارس قاعدة البيانات"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # إنشاء فهارس للحقول المهمة
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_facebook_id ON cleaned_data(facebook_id)",
                "CREATE INDEX IF NOT EXISTS idx_email ON cleaned_data(email)",
                "CREATE INDEX IF NOT EXISTS idx_phone ON cleaned_data(phone)",
                "CREATE INDEX IF NOT EXISTS idx_fullname ON cleaned_data(fullname)",
                "CREATE INDEX IF NOT EXISTS idx_hometown ON cleaned_data(hometown)",
                "CREATE INDEX IF NOT EXISTS idx_country ON cleaned_data(country)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
                self.log(f"🔍 تم تحديث فهرس")
            
            conn.commit()
            conn.close()
            
            self.log(f"✅ تم تحديث جميع الفهارس للبحث السريع")
            
        except Exception as e:
            self.log(f"❌ خطأ في تحديث الفهارس: {str(e)}")

    def check_database(self):
        """فحص قاعدة البيانات"""
        if not self.db_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد قاعدة البيانات")
            return
        
        db_path = Path(self.db_folder.get()) / self.DB_NAME
        
        if not db_path.exists():
            messagebox.showinfo("معلومات", "قاعدة البيانات غير موجودة")
            return
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM cleaned_data")
            count = cursor.fetchone()[0]
            
            # عينة من البيانات
            cursor.execute("SELECT * FROM cleaned_data LIMIT 5")
            sample = cursor.fetchall()
            
            # حجم الملف
            file_size = db_path.stat().st_size / (1024 * 1024)  # MB
            
            conn.close()
            
            # عرض النتائج
            info = f"📊 إحصائيات قاعدة البيانات:\n\n"
            info += f"📁 الملف: {db_path.name}\n"
            info += f"📊 عدد السجلات: {count:,}\n"
            info += f"📋 عدد الأعمدة: {len(self.HEADERS)}\n"
            info += f"💾 حجم الملف: {file_size:.2f} MB\n\n"
            
            if sample:
                info += "📋 عينة من البيانات:\n"
                for i, row in enumerate(sample):
                    facebook_id = row[1] if len(row) > 1 else "غير متوفر"
                    fullname = row[11] if len(row) > 11 else "غير متوفر"
                    info += f"السجل {i+1}: {facebook_id} - {fullname}\n"
            
            messagebox.showinfo("معلومات قاعدة البيانات", info)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فحص قاعدة البيانات: {str(e)}")


def main():
    """تشغيل أداة الإدراج"""
    root = tk.Tk()
    app = AppendDatabaseInserter(root)
    root.mainloop()


if __name__ == "__main__":
    main()

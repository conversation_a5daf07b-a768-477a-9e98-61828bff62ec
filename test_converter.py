#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لمحول النصوص إلى CSV
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # محاولة استيراد الكلاس الرئيسي
    from txt_to_csv_converter import ConversionApp
    print("✅ تم استيراد الكلاس بنجاح!")

    # محاولة إنشاء كائن من الكلاس (بدون root لأنه للاختبار فقط)
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة للاختبار
    app = ConversionApp(root)
    print("✅ تم إنشاء كائن من الكلاس بنجاح!")
    
    # التحقق من وجود الدوال الأساسية
    required_methods = [
        'init_variables',
        'create_widgets', 
        'search_thread',
        'match_criteria',
        'convert_file',
        'get_line_count',
        'should_include_line'
    ]
    
    for method in required_methods:
        if hasattr(app, method):
            print(f"✅ الدالة {method} موجودة")
        else:
            print(f"❌ الدالة {method} مفقودة")
    
    print("\n🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام.")
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
except Exception as e:
    print(f"❌ خطأ عام: {e}")

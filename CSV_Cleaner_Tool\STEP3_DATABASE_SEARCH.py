#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 أداة البحث في قاعدة البيانات - المرحلة الثالثة
Database Search Tool - Step 3
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sqlite3
import csv
import os
import shutil
from pathlib import Path
from threading import Thread, Event
import time
import json

# محاولة استيراد pandas و duckdb للأداء المحسن
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ pandas غير متوفر - سيتم استخدام الطريقة التقليدية")

try:
    import duckdb
    DUCKDB_AVAILABLE = True
except ImportError:
    DUCKDB_AVAILABLE = False
    print("⚠️ DuckDB غير متوفر - سيتم استخدام SQLite")

class DatabaseSearchTool:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 أداة البحث في قاعدة البيانات - المرحلة الثالثة")
        self.root.geometry("1200x800")
        
        # النسق الجديد (19 عمود)
        self.HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # أسماء الحقول للعرض
        self.FIELD_LABELS = {
            'facebook_id': 'Facebook ID',
            'email': 'Email',
            'phone': 'Phone',
            'religion': 'Religion',
            'birthday_year': 'Birthday Year',
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'gender': 'Gender',
            'link': 'Link',
            'username': 'Username',
            'fullname': 'Fullname',
            'beo': 'Beo',
            'company': 'Company',
            'title': 'Title',
            'hometown': 'Hometown',
            'country': 'Country',
            'education': 'Education',
            'user': 'User',
            'status': 'Status'
        }
        
        self.stop_event = Event()
        self.init_variables()
        self.setup_ui()

    def init_variables(self):
        """تهيئة المتغيرات"""
        # مسارات الملفات
        self.db_path = tk.StringVar()
        self.keywords_file = tk.StringVar()
        self.output_path = tk.StringVar()
        
        # متغيرات البحث
        self.search_values = {}
        for header in self.HEADERS:
            self.search_values[header] = tk.StringVar()
        
        self.search_values['keywords'] = tk.StringVar()
        self.search_values['limit'] = tk.StringVar(value="1000")
        
        # متغيرات اختيار الحقول للإخراج
        self.output_fields = {}
        for header in self.HEADERS:
            self.output_fields[header] = tk.BooleanVar(value=True)
        
        # متغيرات أخرى
        self.output_format = tk.StringVar(value="csv")
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.search_type = tk.StringVar(value="facebook_id")

        # قائمة قواعد البيانات
        self.database_list = []
        self.search_results = []
        self.is_searching = False
        self.stop_search_flag = False
        self.pause_search_flag = False
        self.resume_search_flag = False

        # متغيرات الحفظ الفوري
        self.output_writer = None
        self.output_file_handle = None
        self.results_saved_count = 0

        # متغيرات حالة البحث للاستكمال
        self.search_state = {
            'current_db_index': 0,
            'current_row_index': 0,
            'total_results': 0,
            'start_time': None
        }

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي مع scrollbar
        main_canvas = tk.Canvas(self.root)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=main_canvas.yview)
        scrollable_frame = ttk.Frame(main_canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        # العنوان
        title_label = tk.Label(scrollable_frame, text="🔍 أداة البحث في قاعدة البيانات",
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)

        # إنشاء الأقسام
        self.create_database_section(scrollable_frame)
        self.create_search_section(scrollable_frame)
        self.create_progress_section(scrollable_frame)
        self.create_results_section(scrollable_frame)
        self.create_output_section(scrollable_frame)
        self.create_control_section(scrollable_frame)

        # تعبئة الشاشة
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_database_section(self, parent):
        """إنشاء قسم اختيار قاعدة البيانات"""
        db_frame = ttk.LabelFrame(parent, text="📁 Select Database", padding=10)
        db_frame.pack(fill='x', pady=(0, 10))
        
        # قائمة قواعد البيانات
        list_frame = ttk.Frame(db_frame)
        list_frame.pack(fill='both', expand=True)
        
        # Listbox مع scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')
        
        self.db_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, height=4)
        self.db_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.db_listbox.yview)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(db_frame)
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Button(buttons_frame, text="➕ Add Database", 
                  command=self.add_database).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="➖ Remove", 
                  command=self.remove_database).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="🗑️ Clear All", 
                  command=self.clear_databases).pack(side='left')

    def create_search_section(self, parent):
        """إنشاء قسم البحث"""
        search_frame = ttk.LabelFrame(parent, text="🔍 Search by Value", padding=10)
        search_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # إنشاء notebook للتبويبات
        notebook = ttk.Notebook(search_frame)
        notebook.pack(fill='both', expand=True)
        
        # تبويب البحث بالحقول
        fields_frame = ttk.Frame(notebook)
        notebook.add(fields_frame, text="البحث بالحقول")
        
        # تبويب اختيار الحقول للإخراج
        output_frame = ttk.Frame(notebook)
        notebook.add(output_frame, text="Select Output Fields")
        
        # تبويب البحث بالملف
        file_frame = ttk.Frame(notebook)
        notebook.add(file_frame, text="Search by File")
        
        self.create_search_fields(fields_frame)
        self.create_output_fields(output_frame)
        self.create_file_search(file_frame)

    def create_search_fields(self, parent):
        """إنشاء حقول البحث"""
        # إطار للحقول مع scrollbar
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # إنشاء الحقول في شبكة
        row = 0
        col = 0
        
        for header in self.HEADERS + ['keywords', 'limit']:
            label_text = self.FIELD_LABELS.get(header, header.title())
            
            ttk.Label(scrollable_frame, text=f"{label_text}:").grid(
                row=row, column=col*2, sticky='w', padx=(0, 5), pady=2)
            
            if header == 'gender':
                # قائمة منسدلة للجنس
                combo = ttk.Combobox(scrollable_frame, 
                                   textvariable=self.search_values[header],
                                   values=["", "male", "female", "ذكر", "أنثى"],
                                   state="readonly", width=25)
                combo.grid(row=row, column=col*2+1, sticky='ew', padx=(0, 10), pady=2)
            else:
                # حقل نص عادي
                entry = ttk.Entry(scrollable_frame, 
                                textvariable=self.search_values[header], width=25)
                entry.grid(row=row, column=col*2+1, sticky='ew', padx=(0, 10), pady=2)
            
            # الانتقال للعمود التالي
            col += 1
            if col >= 2:  # عمودين
                col = 0
                row += 1
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_progress_section(self, parent):
        """إنشاء قسم شريط التقدم"""
        progress_frame = ttk.LabelFrame(parent, text="📊 حالة البحث", padding=10)
        progress_frame.pack(fill='x', pady=10)

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.pack(fill='x', pady=(0, 5))

        # تسمية شريط التقدم
        self.progress_label = ttk.Label(progress_frame, text="جاهز للبحث")
        self.progress_label.pack()

        # إحصائيات البحث
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.pack(fill='x', pady=(10, 0))

        self.stats_label = ttk.Label(stats_frame, text="النتائج: 0 | الوقت: 0s")
        self.stats_label.pack(side='left')

        # أزرار التحكم في البحث
        control_buttons_frame = ttk.Frame(stats_frame)
        control_buttons_frame.pack(side='right')

        self.pause_button = ttk.Button(control_buttons_frame, text="⏸️ إيقاف مؤقت",
                                     command=self.pause_search, state='disabled')
        self.pause_button.pack(side='left', padx=(0, 5))

        self.resume_button = ttk.Button(control_buttons_frame, text="▶️ استكمال",
                                      command=self.resume_search, state='disabled')
        self.resume_button.pack(side='left', padx=(0, 5))

        self.stop_search_button = ttk.Button(control_buttons_frame, text="⏹️ إيقاف نهائي",
                                           command=self.stop_search, state='disabled')
        self.stop_search_button.pack(side='left')

    def create_results_section(self, parent):
        """إنشاء قسم النتائج"""
        results_frame = ttk.LabelFrame(parent, text="📋 نتائج البحث", padding=10)
        results_frame.pack(fill='both', expand=True, pady=10)

        # جدول النتائج
        columns = ('facebook_id', 'email', 'phone', 'first_name', 'last_name', 'gender')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=10)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.results_tree.heading(col, text=self.FIELD_LABELS.get(col, col.title()))
            self.results_tree.column(col, width=120, minwidth=80)

        # شريط التمرير للجدول
        tree_scrollbar_y = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_tree.yview)
        tree_scrollbar_x = ttk.Scrollbar(results_frame, orient="horizontal", command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=tree_scrollbar_y.set, xscrollcommand=tree_scrollbar_x.set)

        # تخطيط الجدول
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        tree_scrollbar_y.grid(row=0, column=1, sticky='ns')
        tree_scrollbar_x.grid(row=1, column=0, sticky='ew')

        # تكوين الشبكة
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(0, weight=1)

        # إحصائيات النتائج
        self.results_info_label = ttk.Label(results_frame, text="لا توجد نتائج")
        self.results_info_label.grid(row=2, column=0, columnspan=2, pady=(10, 0))

    def create_output_fields(self, parent):
        """إنشاء اختيار حقول الإخراج"""
        # إطار للحقول
        fields_frame = ttk.Frame(parent)
        fields_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إنشاء checkboxes في شبكة
        row = 0
        col = 0
        
        for header in self.HEADERS:
            label_text = self.FIELD_LABELS.get(header, header.title())
            
            checkbox = ttk.Checkbutton(fields_frame, 
                                     text=label_text,
                                     variable=self.output_fields[header])
            checkbox.grid(row=row, column=col, sticky='w', padx=10, pady=2)
            
            col += 1
            if col >= 3:  # 3 أعمدة
                col = 0
                row += 1
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="✅ Select All", 
                  command=self.select_all_fields).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="❌ Deselect All", 
                  command=self.deselect_all_fields).pack(side='left')

    def create_file_search(self, parent):
        """إنشاء البحث بالملف"""
        file_frame = ttk.Frame(parent, padding=10)
        file_frame.pack(fill='x')
        
        # ملف الكلمات المفتاحية
        ttk.Label(file_frame, text="Data File:").pack(anchor='w')
        
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill='x', pady=5)
        
        ttk.Entry(path_frame, textvariable=self.keywords_file).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(path_frame, text="📁", command=self.browse_keywords_file).pack(side='right')
        
        # خيارات البحث
        search_options_frame = ttk.LabelFrame(file_frame, text="Search By:", padding=5)
        search_options_frame.pack(fill='x', pady=10)
        
        self.search_type = tk.StringVar(value="id")
        
        ttk.Radiobutton(search_options_frame, text="📱 ID", 
                       variable=self.search_type, value="id").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="📞 Phone", 
                       variable=self.search_type, value="phone").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="📧 Email", 
                       variable=self.search_type, value="email").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="🔑 Keywords", 
                       variable=self.search_type, value="keywords").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="👤 Username", 
                       variable=self.search_type, value="username").pack(side='left')

    def create_output_section(self, parent):
        """إنشاء قسم الإخراج"""
        output_frame = ttk.LabelFrame(parent, text="💾 Output Settings", padding=10)
        output_frame.pack(fill='x', pady=(0, 10))
        
        # مسار الإخراج
        ttk.Label(output_frame, text="Output File:").pack(anchor='w')
        
        path_frame = ttk.Frame(output_frame)
        path_frame.pack(fill='x', pady=5)
        
        ttk.Entry(path_frame, textvariable=self.output_path).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(path_frame, text="📁", command=self.browse_output).pack(side='right')
        
        # تنسيق الإخراج
        format_frame = ttk.Frame(output_frame)
        format_frame.pack(fill='x', pady=5)
        
        ttk.Label(format_frame, text="Format:").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(format_frame, text="CSV", 
                       variable=self.output_format, value="csv").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(format_frame, text="TXT", 
                       variable=self.output_format, value="txt").pack(side='left')

    def create_control_section(self, parent):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(parent, text="🎮 التحكم", padding=10)
        control_frame.pack(fill='x', pady=10)

        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x')

        self.search_button = ttk.Button(buttons_frame, text="🔍 بدء البحث",
                                       command=self.start_search)
        self.search_button.pack(side='left', padx=(0, 10))

        # زر تنظيف النتائج
        clear_button = ttk.Button(buttons_frame, text="🗑️ مسح النتائج",
                                 command=self.clear_results)
        clear_button.pack(side='left', padx=(0, 10))

        # زر فتح مجلد الإخراج
        open_folder_button = ttk.Button(buttons_frame, text="📁 فتح مجلد الإخراج",
                                       command=self.open_output_folder)
        open_folder_button.pack(side='left')

    def check_disk_space(self, path, min_space_gb=1):
        """فحص مساحة القرص المتاحة"""
        try:
            if os.name == 'nt':  # Windows
                import shutil
                total, used, free = shutil.disk_usage(os.path.dirname(path))
                free_gb = free / (1024**3)
            else:  # Linux/Mac
                statvfs = os.statvfs(os.path.dirname(path))
                free_gb = (statvfs.f_frsize * statvfs.f_bavail) / (1024**3)

            return free_gb >= min_space_gb, free_gb
        except Exception as e:
            print(f"خطأ في فحص مساحة القرص: {e}")
            return True, 0  # افتراض وجود مساحة كافية

    def clear_results(self):
        """مسح النتائج"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.search_results = []
        self.results_info_label.config(text="لا توجد نتائج")
        self.progress_var.set(0)
        self.progress_label.config(text="جاهز للبحث")
        self.stats_label.config(text="النتائج: 0 | الوقت: 0s")

    def open_output_folder(self):
        """فتح مجلد الإخراج"""
        output_path = self.output_path.get()
        if output_path:
            import subprocess
            import platform

            folder_path = os.path.dirname(output_path)
            if os.path.exists(folder_path):
                if platform.system() == "Windows":
                    subprocess.run(f'explorer "{folder_path}"', shell=True)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", folder_path])
                else:  # Linux
                    subprocess.run(["xdg-open", folder_path])
            else:
                messagebox.showwarning("تنبيه", "مجلد الإخراج غير موجود")
        else:
            messagebox.showwarning("تنبيه", "لم يتم تحديد مسار الإخراج")

    def add_database(self):
        """إضافة قاعدة بيانات"""
        file_path = filedialog.askopenfilename(
            title="اختر قاعدة البيانات",
            filetypes=[
                ("Database files", "*.db *.sqlite"),
                ("Text files", "*.txt"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            if file_path not in self.database_list:
                self.database_list.append(file_path)
                file_name = os.path.basename(file_path)
                file_type = "DB" if file_path.lower().endswith(('.db', '.sqlite')) else "TXT/CSV"
                self.db_listbox.insert(tk.END, f"{file_name} ({file_type})")

    def remove_database(self):
        """حذف قاعدة بيانات"""
        selection = self.db_listbox.curselection()
        if selection:
            index = selection[0]
            self.db_listbox.delete(index)
            del self.database_list[index]

    def clear_databases(self):
        """مسح جميع قواعد البيانات"""
        self.db_listbox.delete(0, tk.END)
        self.database_list.clear()

    def browse_keywords_file(self):
        """تصفح ملف الكلمات المفتاحية"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف البيانات",
            filetypes=[
                ("Text files", "*.txt"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.keywords_file.set(file_path)

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        file_path = filedialog.asksaveasfilename(
            title="اختر ملف الإخراج",
            defaultextension=".csv",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.output_path.set(file_path)

    def select_all_fields(self):
        """تحديد جميع الحقول"""
        for var in self.output_fields.values():
            var.set(True)

    def deselect_all_fields(self):
        """إلغاء تحديد جميع الحقول"""
        for var in self.output_fields.values():
            var.set(False)

    def start_search(self):
        """بدء البحث"""
        if not self.database_list:
            messagebox.showwarning("تنبيه", "يرجى اختيار قاعدة بيانات واحدة على الأقل")
            return

        if not self.output_path.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف الإخراج")
            return

        # تنظيف النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.search_results = []

        # فحص مساحة القرص
        has_space, free_gb = self.check_disk_space(self.output_path.get(), 0.5)
        if not has_space:
            messagebox.showerror("خطأ", f"مساحة القرص غير كافية! متوفر: {free_gb:.2f} GB")
            return

        # تعطيل أزرار التحكم
        self.search_button.config(state='disabled')
        self.pause_button.config(state='normal')
        self.stop_search_button.config(state='normal')
        self.stop_event.clear()

        # إعادة تعيين حالة البحث
        self.search_state = {
            'current_db_index': 0,
            'current_row_index': 0,
            'total_results': 0,
            'start_time': time.time()
        }

        # تحديث شريط التقدم
        self.progress_var.set(0)
        self.progress_label.config(text="بدء البحث...")
        self.stats_label.config(text="النتائج: 0 | الوقت: 0s")

        # بدء البحث في thread منفصل
        Thread(target=self.search_thread, daemon=True).start()

    def pause_search(self):
        """إيقاف البحث مؤقتاً"""
        self.pause_search_flag = True
        self.pause_button.config(state='disabled')
        self.resume_button.config(state='normal')
        self.progress_label.config(text="تم إيقاف البحث مؤقتاً...")
        print("⏸️ تم إيقاف البحث مؤقتاً")

    def resume_search(self):
        """استكمال البحث"""
        self.pause_search_flag = False
        self.resume_search_flag = True
        self.pause_button.config(state='normal')
        self.resume_button.config(state='disabled')
        self.progress_label.config(text="استكمال البحث...")
        print("▶️ استكمال البحث...")

        # استكمال البحث في thread منفصل
        Thread(target=self.search_thread, daemon=True).start()

    def stop_search(self):
        """إيقاف البحث نهائياً"""
        self.stop_event.set()
        self.stop_search_flag = True
        self.pause_search_flag = False
        self.progress_label.config(text="جاري إيقاف البحث...")

        # إغلاق ملف الإخراج إذا كان مفتوحاً
        if self.output_file_handle:
            try:
                self.output_file_handle.close()
                self.output_file_handle = None
                self.output_writer = None
            except:
                pass

        # إعادة تفعيل الأزرار
        self.search_button.config(state='normal')
        self.pause_button.config(state='disabled')
        self.resume_button.config(state='disabled')
        self.stop_search_button.config(state='disabled')

        print("⏹️ تم إيقاف البحث نهائياً")

    def search_thread(self):
        """تنفيذ البحث في thread منفصل"""
        try:
            # استخدام الوقت المحفوظ أو وقت جديد
            if not self.resume_search_flag:
                start_time = time.time()
                self.search_state['start_time'] = start_time
                total_results = 0

                # طباعة معلومات البحث في CMD
                print("=" * 60)
                print("🔍 بدء عملية البحث في قاعدة البيانات")
                print("=" * 60)
                print(f"عدد قواعد البيانات: {len(self.database_list)}")
                for i, db in enumerate(self.database_list, 1):
                    print(f"  {i}. {os.path.basename(db)}")
                print(f"ملف الإخراج: {self.output_path.get()}")
                print(f"تنسيق الإخراج: {self.output_format.get().upper()}")
                print("-" * 60)
            else:
                # استكمال البحث
                start_time = self.search_state['start_time']
                total_results = self.search_state['total_results']
                print(f"▶️ استكمال البحث من النتيجة رقم {total_results + 1}")
                self.resume_search_flag = False

            # إنشاء/فتح ملف الإخراج
            output_file = self.output_path.get()

            # فتح الملف للكتابة (append إذا كان استكمال)
            mode = 'a' if total_results > 0 else 'w'
            self.output_file_handle = open(output_file, mode, newline='', encoding='utf-8')

            if self.output_format.get() == 'csv':
                self.output_writer = csv.writer(self.output_file_handle, delimiter='|')
            else:
                self.output_writer = None

            # كتابة الرؤوس إذا كان ملف جديد
            if total_results == 0:
                headers = self.get_selected_headers()
                if self.output_format.get() == 'csv':
                    self.output_writer.writerow(headers)
                else:
                    self.output_file_handle.write('|'.join(headers) + '\n')
                self.output_file_handle.flush()  # حفظ فوري

            # البحث في كل قاعدة بيانات
            start_db_index = self.search_state.get('current_db_index', 0)

            for i in range(start_db_index, len(self.database_list)):
                if self.stop_event.is_set():
                    break

                # فحص الإيقاف المؤقت
                while self.pause_search_flag and not self.stop_event.is_set():
                    time.sleep(0.1)
                    continue

                if self.stop_event.is_set():
                    break

                db_path = self.database_list[i]
                self.search_state['current_db_index'] = i

                # طباعة معلومات قاعدة البيانات الحالية
                db_name = os.path.basename(db_path)
                file_type = "قاعدة بيانات" if db_path.lower().endswith(('.db', '.sqlite')) else "ملف نصي"
                print(f"\n🔍 البحث في {file_type}: {db_name}")

                # تحديث شريط التقدم
                progress = (i / len(self.database_list)) * 100
                self.progress_var.set(progress)
                self.progress_label.config(text=f"البحث في قاعدة البيانات {i+1}/{len(self.database_list)}")

                # تحديد نوع الملف والبحث المناسب
                if db_path.lower().endswith(('.db', '.sqlite')):
                    results = self.search_database_optimized(db_path)
                elif db_path.lower().endswith('.csv'):
                    results = self.search_csv_file_optimized(db_path)
                elif db_path.lower().endswith('.txt'):
                    results = self.search_txt_file_optimized(db_path)
                else:
                    print(f"⚠️ نوع ملف غير مدعوم: {db_path}")
                    continue

                print(f"✅ تم العثور على {len(results)} نتيجة في {db_name}")

                # كتابة النتائج وعرضها في الجدول
                for result in results:
                    if self.stop_event.is_set():
                        break

                    # فحص الإيقاف المؤقت
                    while self.pause_search_flag and not self.stop_event.is_set():
                        time.sleep(0.1)
                        continue

                    if self.stop_event.is_set():
                        break

                    # طباعة النتيجة في CMD (كل 100 نتيجة لتوفير الذاكرة)
                    if total_results % 100 == 0:
                        print(f"نتيجة {total_results + 1}: {result}")

                    # إضافة النتيجة للجدول
                    self.add_result_to_tree(result)

                    # حفظ فوري للنتيجة
                    if self.output_format.get() == 'csv':
                        self.output_writer.writerow(result)
                    else:
                        self.output_file_handle.write('|'.join(str(x) for x in result) + '\n')

                    # حفظ فوري كل 50 نتيجة
                    if total_results % 50 == 0:
                        self.output_file_handle.flush()

                    total_results += 1
                    self.search_state['total_results'] = total_results

                    # تحديث الإحصائيات
                    elapsed_time = int(time.time() - start_time)
                    self.stats_label.config(text=f"النتائج: {total_results} | الوقت: {elapsed_time}s")

                    # تحديث الواجهة كل 10 نتائج
                    if total_results % 10 == 0:
                        self.root.update_idletasks()

                    # فحص مساحة القرص كل 1000 نتيجة
                    if total_results % 1000 == 0:
                        has_space, free_gb = self.check_disk_space(self.output_path.get(), 0.1)
                        if not has_space:
                            print(f"⚠️ تحذير: مساحة القرص منخفضة ({free_gb:.2f} GB)")
                            self.pause_search()
                            messagebox.showwarning("تحذير", f"مساحة القرص منخفضة: {free_gb:.2f} GB\nتم إيقاف البحث مؤقتاً")
                            break
            
            # حفظ نهائي
            if self.output_file_handle:
                self.output_file_handle.flush()
                self.output_file_handle.close()
                self.output_file_handle = None
                self.output_writer = None

            if not self.stop_event.is_set() and not self.pause_search_flag:
                elapsed_time = int(time.time() - start_time)
                self.progress_var.set(100)
                self.progress_label.config(text=f"اكتمل البحث! تم العثور على {total_results} نتيجة")
                self.stats_label.config(text=f"النتائج: {total_results} | الوقت: {elapsed_time}s")
                self.results_info_label.config(text=f"تم العثور على {total_results} نتيجة")

                # طباعة النتائج النهائية في CMD
                print("\n" + "=" * 60)
                print("🎉 اكتمل البحث!")
                print("=" * 60)
                print(f"📊 إجمالي النتائج: {total_results}")
                print(f"⏱️ الوقت المستغرق: {elapsed_time} ثانية")
                print(f"💾 ملف الإخراج: {output_file}")
                print("=" * 60)

                messagebox.showinfo("مكتمل", f"تم العثور على {total_results} نتيجة في {elapsed_time} ثانية")

                # إعادة تعيين حالة البحث
                self.search_state = {
                    'current_db_index': 0,
                    'current_row_index': 0,
                    'total_results': 0,
                    'start_time': None
                }
            elif self.pause_search_flag:
                print(f"\n⏸️ تم إيقاف البحث مؤقتاً عند النتيجة {total_results}")
                self.progress_label.config(text=f"متوقف مؤقتاً عند النتيجة {total_results}")
            else:
                print("\n⏹️ تم إيقاف البحث بواسطة المستخدم")
                self.progress_label.config(text="تم إيقاف البحث")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
            print(f"خطأ في البحث: {e}")

        finally:
            # إغلاق ملف الإخراج إذا كان مفتوحاً
            if self.output_file_handle:
                try:
                    self.output_file_handle.close()
                    self.output_file_handle = None
                    self.output_writer = None
                except:
                    pass

            # إعادة تفعيل الأزرار
            if not self.pause_search_flag:
                self.search_button.config(state='normal')
                self.pause_button.config(state='disabled')
                self.resume_button.config(state='disabled')
                self.stop_search_button.config(state='disabled')

    def add_result_to_tree(self, result):
        """إضافة نتيجة للجدول"""
        try:
            # عرض أول 6 أعمدة فقط في الجدول
            display_result = result[:6] if len(result) >= 6 else result
            self.results_tree.insert('', 'end', values=display_result)

            # تحديث الواجهة
            self.root.update_idletasks()
        except Exception as e:
            print(f"خطأ في إضافة النتيجة للجدول: {e}")

    def search_csv_file(self, file_path):
        """البحث في ملف CSV"""
        results = []
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                # قراءة الملف كـ CSV
                reader = csv.reader(f, delimiter='|')

                # تخطي الرأس إذا وجد
                try:
                    first_row = next(reader)
                    if not any(self.matches_search_criteria(first_row)):
                        # إذا لم يطابق الصف الأول معايير البحث، فهو رأس
                        pass
                    else:
                        # الصف الأول يحتوي على بيانات
                        if self.matches_search_criteria(first_row):
                            results.append(self.format_result(first_row))
                except StopIteration:
                    return results

                # البحث في باقي الصفوف
                for row in reader:
                    if self.stop_event.is_set():
                        break

                    if self.matches_search_criteria(row):
                        results.append(self.format_result(row))

        except Exception as e:
            print(f"خطأ في قراءة ملف CSV {file_path}: {e}")

        return results

    def search_txt_file(self, file_path):
        """البحث في ملف TXT"""
        results = []
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    if self.stop_event.is_set():
                        break

                    # تقسيم السطر بالفاصل |
                    row = line.strip().split('|')

                    if self.matches_search_criteria(row):
                        results.append(self.format_result(row))

        except Exception as e:
            print(f"خطأ في قراءة ملف TXT {file_path}: {e}")

        return results

    def matches_search_criteria(self, row):
        """فحص ما إذا كان الصف يطابق معايير البحث"""
        if not row:
            return False

        # التأكد من أن الصف يحتوي على بيانات كافية
        if len(row) < len(self.HEADERS):
            # إضافة قيم فارغة للأعمدة المفقودة
            row.extend([''] * (len(self.HEADERS) - len(row)))

        # فحص كل معيار بحث
        for i, header in enumerate(self.HEADERS):
            search_value = self.search_values[header].get().strip()
            if search_value:
                if i < len(row):
                    cell_value = str(row[i]).lower()
                    if search_value.lower() not in cell_value:
                        return False

        # فحص الكلمات المفتاحية
        keywords = self.search_values['keywords'].get().strip()
        if keywords:
            keywords_list = [k.strip().lower() for k in keywords.split(',')]
            row_text = ' '.join(str(cell).lower() for cell in row)
            if not any(keyword in row_text for keyword in keywords_list):
                return False

        return True

    def format_result(self, row):
        """تنسيق النتيجة للإخراج"""
        # التأكد من أن الصف يحتوي على العدد الصحيح من الأعمدة
        if len(row) < len(self.HEADERS):
            row.extend([''] * (len(self.HEADERS) - len(row)))
        elif len(row) > len(self.HEADERS):
            row = row[:len(self.HEADERS)]

        # إرجاع الحقول المحددة فقط
        selected_fields = []
        for i, header in enumerate(self.HEADERS):
            if self.output_fields[header].get():
                selected_fields.append(row[i] if i < len(row) else '')

        return selected_fields

    def search_database(self, db_path):
        """البحث في قاعدة بيانات واحدة"""
        results = []
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # بناء الاستعلام
            query, params = self.build_query()
            
            cursor.execute(query, params)
            
            while True:
                if self.stop_event.is_set():
                    break
                
                rows = cursor.fetchmany(1000)
                if not rows:
                    break
                
                for row in rows:
                    # فلترة النتائج حسب الحقول المختارة
                    filtered_row = self.filter_result(row)
                    results.append(filtered_row)
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في البحث في {db_path}: {str(e)}")
        
        return results

    def build_query(self):
        """بناء استعلام SQL"""
        # الحقول المختارة
        selected_fields = []
        for header in self.HEADERS:
            if self.output_fields[header].get():
                selected_fields.append(header)
        
        if not selected_fields:
            selected_fields = self.HEADERS
        
        select_clause = ', '.join(selected_fields)
        query = f"SELECT {select_clause} FROM cleaned_data"
        
        # شروط البحث
        where_conditions = []
        params = []
        
        # البحث بالحقول
        for header in self.HEADERS:
            value = self.search_values[header].get().strip()
            if value:
                where_conditions.append(f"{header} LIKE ?")
                params.append(f"%{value}%")
        
        # البحث بالكلمات المفتاحية
        keywords = self.search_values['keywords'].get().strip()
        if keywords:
            keyword_conditions = []
            for header in self.HEADERS:
                keyword_conditions.append(f"{header} LIKE ?")
                params.append(f"%{keywords}%")
            
            if keyword_conditions:
                where_conditions.append(f"({' OR '.join(keyword_conditions)})")
        
        # إضافة شروط WHERE
        if where_conditions:
            query += " WHERE " + " AND ".join(where_conditions)
        
        # إضافة LIMIT
        limit = self.search_values['limit'].get().strip()
        if limit and limit.isdigit():
            query += f" LIMIT {limit}"
        
        return query, params

    def filter_result(self, row):
        """فلترة النتيجة حسب الحقول المختارة"""
        filtered = []
        for i, header in enumerate(self.HEADERS):
            if self.output_fields[header].get():
                filtered.append(row[i] if i < len(row) else '')
        return filtered

    def get_selected_headers(self):
        """الحصول على رؤوس الحقول المختارة"""
        headers = []
        for header in self.HEADERS:
            if self.output_fields[header].get():
                headers.append(self.FIELD_LABELS.get(header, header))
        return headers

    def search_database_optimized(self, db_path):
        """البحث المحسن في قاعدة بيانات SQLite"""
        if DUCKDB_AVAILABLE:
            return self.search_database_duckdb(db_path)
        else:
            return self.search_database(db_path)

    def search_csv_file_optimized(self, file_path):
        """البحث المحسن في ملف CSV"""
        if PANDAS_AVAILABLE:
            return self.search_csv_pandas(file_path)
        else:
            return self.search_csv_file(file_path)

    def search_txt_file_optimized(self, file_path):
        """البحث المحسن في ملف TXT"""
        if PANDAS_AVAILABLE:
            return self.search_txt_pandas(file_path)
        else:
            return self.search_txt_file(file_path)

    def search_database_duckdb(self, db_path):
        """البحث باستخدام DuckDB للأداء المحسن"""
        results = []
        try:
            print("🚀 استخدام DuckDB للبحث المحسن...")
            # العودة للطريقة التقليدية حالياً
            results = self.search_database(db_path)
        except Exception as e:
            print(f"خطأ في البحث بـ DuckDB: {e}")
            results = self.search_database(db_path)
        return results

    def search_csv_pandas(self, file_path):
        """البحث في CSV باستخدام pandas للأداء المحسن"""
        results = []
        try:
            print("🐼 استخدام pandas للبحث المحسن...")

            # قراءة الملف باستخدام pandas مع معالجة الأخطاء
            df = pd.read_csv(file_path, delimiter='|', encoding='utf-8',
                           header=0, low_memory=False, dtype=str,
                           na_filter=False, on_bad_lines='skip')

            # التأكد من وجود الأعمدة المطلوبة
            if len(df.columns) < len(self.HEADERS):
                # إضافة أعمدة مفقودة
                for i in range(len(df.columns), len(self.HEADERS)):
                    df[self.HEADERS[i]] = ''

            # إعادة تسمية الأعمدة
            df.columns = self.HEADERS[:len(df.columns)]

            # تطبيق الفلاتر
            mask = pd.Series([True] * len(df))

            for header in self.HEADERS:
                if header in df.columns:
                    search_value = self.search_values[header].get().strip()
                    if search_value:
                        mask &= df[header].astype(str).str.contains(search_value, case=False, na=False, regex=False)

            # البحث بالكلمات المفتاحية
            keywords = self.search_values['keywords'].get().strip()
            if keywords:
                keywords_list = [k.strip() for k in keywords.split(',')]
                keyword_mask = pd.Series([False] * len(df))

                for keyword in keywords_list:
                    for header in self.HEADERS:
                        if header in df.columns:
                            keyword_mask |= df[header].astype(str).str.contains(keyword, case=False, na=False, regex=False)

                mask &= keyword_mask

            # تطبيق الحد الأقصى
            limit = int(self.search_values['limit'].get() or 1000)
            filtered_df = df[mask].head(limit)

            # تحويل النتائج
            for _, row in filtered_df.iterrows():
                if self.stop_event.is_set() or self.pause_search_flag:
                    break
                formatted_result = self.format_result(row.tolist())
                if formatted_result:
                    results.append(formatted_result)

        except Exception as e:
            print(f"خطأ في البحث بـ pandas: {e}")
            # العودة للطريقة التقليدية
            results = self.search_csv_file(file_path)

        return results

    def search_txt_pandas(self, file_path):
        """البحث في TXT باستخدام pandas للأداء المحسن"""
        try:
            print("🐼 استخدام pandas لملف TXT...")
            return self.search_csv_pandas(file_path)
        except Exception as e:
            print(f"خطأ في البحث بـ pandas: {e}")
            return self.search_txt_file(file_path)


def main():
    root = tk.Tk()
    DatabaseSearchTool(root)
    root.mainloop()


if __name__ == "__main__":
    main()

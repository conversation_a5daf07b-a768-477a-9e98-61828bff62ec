#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 أداة البحث في قاعدة البيانات - المرحلة الثالثة
Database Search Tool - Step 3
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sqlite3
import csv
import os
from pathlib import Path
from threading import Thread, Event

class DatabaseSearchTool:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 أداة البحث في قاعدة البيانات - المرحلة الثالثة")
        self.root.geometry("1200x800")
        
        # النسق الجديد (19 عمود)
        self.HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # أسماء الحقول للعرض
        self.FIELD_LABELS = {
            'facebook_id': 'Facebook ID',
            'email': 'Email',
            'phone': 'Phone',
            'religion': 'Religion',
            'birthday_year': 'Birthday Year',
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'gender': 'Gender',
            'link': 'Link',
            'username': 'Username',
            'fullname': 'Fullname',
            'beo': 'Beo',
            'company': 'Company',
            'title': 'Title',
            'hometown': 'Hometown',
            'country': 'Country',
            'education': 'Education',
            'user': 'User',
            'status': 'Status'
        }
        
        self.stop_event = Event()
        self.init_variables()
        self.setup_ui()

    def init_variables(self):
        """تهيئة المتغيرات"""
        # مسارات الملفات
        self.db_path = tk.StringVar()
        self.keywords_file = tk.StringVar()
        self.output_path = tk.StringVar()
        
        # متغيرات البحث
        self.search_values = {}
        for header in self.HEADERS:
            self.search_values[header] = tk.StringVar()
        
        self.search_values['keywords'] = tk.StringVar()
        self.search_values['limit'] = tk.StringVar(value="1000")
        
        # متغيرات اختيار الحقول للإخراج
        self.output_fields = {}
        for header in self.HEADERS:
            self.output_fields[header] = tk.BooleanVar(value=True)
        
        # متغيرات أخرى
        self.output_format = tk.StringVar(value="csv")
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        
        # قائمة قواعد البيانات
        self.database_list = []

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = tk.Label(main_frame, text="🔍 أداة البحث في قاعدة البيانات", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إنشاء الأقسام
        self.create_database_section(main_frame)
        self.create_search_section(main_frame)
        self.create_output_section(main_frame)
        self.create_control_section(main_frame)

    def create_database_section(self, parent):
        """إنشاء قسم اختيار قاعدة البيانات"""
        db_frame = ttk.LabelFrame(parent, text="📁 Select Database", padding=10)
        db_frame.pack(fill='x', pady=(0, 10))
        
        # قائمة قواعد البيانات
        list_frame = ttk.Frame(db_frame)
        list_frame.pack(fill='both', expand=True)
        
        # Listbox مع scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')
        
        self.db_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, height=4)
        self.db_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.db_listbox.yview)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(db_frame)
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Button(buttons_frame, text="➕ Add Database", 
                  command=self.add_database).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="➖ Remove", 
                  command=self.remove_database).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="🗑️ Clear All", 
                  command=self.clear_databases).pack(side='left')

    def create_search_section(self, parent):
        """إنشاء قسم البحث"""
        search_frame = ttk.LabelFrame(parent, text="🔍 Search by Value", padding=10)
        search_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # إنشاء notebook للتبويبات
        notebook = ttk.Notebook(search_frame)
        notebook.pack(fill='both', expand=True)
        
        # تبويب البحث بالحقول
        fields_frame = ttk.Frame(notebook)
        notebook.add(fields_frame, text="البحث بالحقول")
        
        # تبويب اختيار الحقول للإخراج
        output_frame = ttk.Frame(notebook)
        notebook.add(output_frame, text="Select Output Fields")
        
        # تبويب البحث بالملف
        file_frame = ttk.Frame(notebook)
        notebook.add(file_frame, text="Search by File")
        
        self.create_search_fields(fields_frame)
        self.create_output_fields(output_frame)
        self.create_file_search(file_frame)

    def create_search_fields(self, parent):
        """إنشاء حقول البحث"""
        # إطار للحقول مع scrollbar
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # إنشاء الحقول في شبكة
        row = 0
        col = 0
        
        for header in self.HEADERS + ['keywords', 'limit']:
            label_text = self.FIELD_LABELS.get(header, header.title())
            
            ttk.Label(scrollable_frame, text=f"{label_text}:").grid(
                row=row, column=col*2, sticky='w', padx=(0, 5), pady=2)
            
            if header == 'gender':
                # قائمة منسدلة للجنس
                combo = ttk.Combobox(scrollable_frame, 
                                   textvariable=self.search_values[header],
                                   values=["", "male", "female", "ذكر", "أنثى"],
                                   state="readonly", width=25)
                combo.grid(row=row, column=col*2+1, sticky='ew', padx=(0, 10), pady=2)
            else:
                # حقل نص عادي
                entry = ttk.Entry(scrollable_frame, 
                                textvariable=self.search_values[header], width=25)
                entry.grid(row=row, column=col*2+1, sticky='ew', padx=(0, 10), pady=2)
            
            # الانتقال للعمود التالي
            col += 1
            if col >= 2:  # عمودين
                col = 0
                row += 1
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_output_fields(self, parent):
        """إنشاء اختيار حقول الإخراج"""
        # إطار للحقول
        fields_frame = ttk.Frame(parent)
        fields_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إنشاء checkboxes في شبكة
        row = 0
        col = 0
        
        for header in self.HEADERS:
            label_text = self.FIELD_LABELS.get(header, header.title())
            
            checkbox = ttk.Checkbutton(fields_frame, 
                                     text=label_text,
                                     variable=self.output_fields[header])
            checkbox.grid(row=row, column=col, sticky='w', padx=10, pady=2)
            
            col += 1
            if col >= 3:  # 3 أعمدة
                col = 0
                row += 1
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="✅ Select All", 
                  command=self.select_all_fields).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="❌ Deselect All", 
                  command=self.deselect_all_fields).pack(side='left')

    def create_file_search(self, parent):
        """إنشاء البحث بالملف"""
        file_frame = ttk.Frame(parent, padding=10)
        file_frame.pack(fill='x')
        
        # ملف الكلمات المفتاحية
        ttk.Label(file_frame, text="Data File:").pack(anchor='w')
        
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill='x', pady=5)
        
        ttk.Entry(path_frame, textvariable=self.keywords_file).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(path_frame, text="📁", command=self.browse_keywords_file).pack(side='right')
        
        # خيارات البحث
        search_options_frame = ttk.LabelFrame(file_frame, text="Search By:", padding=5)
        search_options_frame.pack(fill='x', pady=10)
        
        self.search_type = tk.StringVar(value="id")
        
        ttk.Radiobutton(search_options_frame, text="📱 ID", 
                       variable=self.search_type, value="id").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="📞 Phone", 
                       variable=self.search_type, value="phone").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="📧 Email", 
                       variable=self.search_type, value="email").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="🔑 Keywords", 
                       variable=self.search_type, value="keywords").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(search_options_frame, text="👤 Username", 
                       variable=self.search_type, value="username").pack(side='left')

    def create_output_section(self, parent):
        """إنشاء قسم الإخراج"""
        output_frame = ttk.LabelFrame(parent, text="💾 Output Settings", padding=10)
        output_frame.pack(fill='x', pady=(0, 10))
        
        # مسار الإخراج
        ttk.Label(output_frame, text="Output File:").pack(anchor='w')
        
        path_frame = ttk.Frame(output_frame)
        path_frame.pack(fill='x', pady=5)
        
        ttk.Entry(path_frame, textvariable=self.output_path).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(path_frame, text="📁", command=self.browse_output).pack(side='right')
        
        # تنسيق الإخراج
        format_frame = ttk.Frame(output_frame)
        format_frame.pack(fill='x', pady=5)
        
        ttk.Label(format_frame, text="Format:").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(format_frame, text="CSV", 
                       variable=self.output_format, value="csv").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(format_frame, text="TXT", 
                       variable=self.output_format, value="txt").pack(side='left')

    def create_control_section(self, parent):
        """إنشاء قسم التحكم"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill='x')
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(side='left')
        
        self.search_button = ttk.Button(buttons_frame, text="🔍 Search", 
                                       command=self.start_search)
        self.search_button.pack(side='left', padx=(0, 5))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ Stop", 
                                     command=self.stop_search, state='disabled')
        self.stop_button.pack(side='left')
        
        # شريط التقدم والحالة
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(side='right', fill='x', expand=True, padx=(20, 0))
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, 
                                          maximum=100)
        self.progress_bar.pack(fill='x', pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame, textvariable=self.status)
        self.status_label.pack(anchor='w')

    def add_database(self):
        """إضافة قاعدة بيانات"""
        file_path = filedialog.askopenfilename(
            title="اختر قاعدة البيانات",
            filetypes=[
                ("Database files", "*.db *.sqlite"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            if file_path not in self.database_list:
                self.database_list.append(file_path)
                self.db_listbox.insert(tk.END, os.path.basename(file_path))

    def remove_database(self):
        """حذف قاعدة بيانات"""
        selection = self.db_listbox.curselection()
        if selection:
            index = selection[0]
            self.db_listbox.delete(index)
            del self.database_list[index]

    def clear_databases(self):
        """مسح جميع قواعد البيانات"""
        self.db_listbox.delete(0, tk.END)
        self.database_list.clear()

    def browse_keywords_file(self):
        """تصفح ملف الكلمات المفتاحية"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف البيانات",
            filetypes=[
                ("Text files", "*.txt"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.keywords_file.set(file_path)

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        file_path = filedialog.asksaveasfilename(
            title="اختر ملف الإخراج",
            defaultextension=".csv",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.output_path.set(file_path)

    def select_all_fields(self):
        """تحديد جميع الحقول"""
        for var in self.output_fields.values():
            var.set(True)

    def deselect_all_fields(self):
        """إلغاء تحديد جميع الحقول"""
        for var in self.output_fields.values():
            var.set(False)

    def start_search(self):
        """بدء البحث"""
        if not self.database_list:
            messagebox.showwarning("تنبيه", "يرجى اختيار قاعدة بيانات واحدة على الأقل")
            return
        
        if not self.output_path.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف الإخراج")
            return
        
        # تعطيل أزرار التحكم
        self.search_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.stop_event.clear()
        
        # بدء البحث في thread منفصل
        Thread(target=self.search_thread, daemon=True).start()

    def stop_search(self):
        """إيقاف البحث"""
        self.stop_event.set()
        self.status.set("جاري إيقاف البحث...")

    def search_thread(self):
        """تنفيذ البحث في thread منفصل"""
        try:
            total_results = 0
            
            # إنشاء ملف الإخراج
            output_file = self.output_path.get()
            
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                if self.output_format.get() == 'csv':
                    writer = csv.writer(f, delimiter='|')
                else:
                    writer = None
                
                # كتابة الرؤوس
                headers = self.get_selected_headers()
                if self.output_format.get() == 'csv':
                    writer.writerow(headers)
                else:
                    f.write('|'.join(headers) + '\n')
                
                # البحث في كل قاعدة بيانات
                for i, db_path in enumerate(self.database_list):
                    if self.stop_event.is_set():
                        break
                    
                    self.status.set(f"البحث في قاعدة البيانات {i+1}/{len(self.database_list)}")
                    self.progress.set((i / len(self.database_list)) * 100)
                    
                    results = self.search_database(db_path)
                    
                    # كتابة النتائج
                    for result in results:
                        if self.stop_event.is_set():
                            break
                        
                        if self.output_format.get() == 'csv':
                            writer.writerow(result)
                        else:
                            f.write('|'.join(str(x) for x in result) + '\n')
                        
                        total_results += 1
            
            if not self.stop_event.is_set():
                self.status.set(f"اكتمل البحث! تم العثور على {total_results} نتيجة")
                self.progress.set(100)
                messagebox.showinfo("مكتمل", f"تم العثور على {total_results} نتيجة")
            else:
                self.status.set("تم إيقاف البحث")
        
        except Exception as e:
            self.status.set(f"خطأ: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث:\n{str(e)}")
        
        finally:
            # إعادة تفعيل الأزرار
            self.search_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def search_database(self, db_path):
        """البحث في قاعدة بيانات واحدة"""
        results = []
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # بناء الاستعلام
            query, params = self.build_query()
            
            cursor.execute(query, params)
            
            while True:
                if self.stop_event.is_set():
                    break
                
                rows = cursor.fetchmany(1000)
                if not rows:
                    break
                
                for row in rows:
                    # فلترة النتائج حسب الحقول المختارة
                    filtered_row = self.filter_result(row)
                    results.append(filtered_row)
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في البحث في {db_path}: {str(e)}")
        
        return results

    def build_query(self):
        """بناء استعلام SQL"""
        # الحقول المختارة
        selected_fields = []
        for header in self.HEADERS:
            if self.output_fields[header].get():
                selected_fields.append(header)
        
        if not selected_fields:
            selected_fields = self.HEADERS
        
        select_clause = ', '.join(selected_fields)
        query = f"SELECT {select_clause} FROM cleaned_data"
        
        # شروط البحث
        where_conditions = []
        params = []
        
        # البحث بالحقول
        for header in self.HEADERS:
            value = self.search_values[header].get().strip()
            if value:
                where_conditions.append(f"{header} LIKE ?")
                params.append(f"%{value}%")
        
        # البحث بالكلمات المفتاحية
        keywords = self.search_values['keywords'].get().strip()
        if keywords:
            keyword_conditions = []
            for header in self.HEADERS:
                keyword_conditions.append(f"{header} LIKE ?")
                params.append(f"%{keywords}%")
            
            if keyword_conditions:
                where_conditions.append(f"({' OR '.join(keyword_conditions)})")
        
        # إضافة شروط WHERE
        if where_conditions:
            query += " WHERE " + " AND ".join(where_conditions)
        
        # إضافة LIMIT
        limit = self.search_values['limit'].get().strip()
        if limit and limit.isdigit():
            query += f" LIMIT {limit}"
        
        return query, params

    def filter_result(self, row):
        """فلترة النتيجة حسب الحقول المختارة"""
        filtered = []
        for i, header in enumerate(self.HEADERS):
            if self.output_fields[header].get():
                filtered.append(row[i] if i < len(row) else '')
        return filtered

    def get_selected_headers(self):
        """الحصول على رؤوس الحقول المختارة"""
        headers = []
        for header in self.HEADERS:
            if self.output_fields[header].get():
                headers.append(self.FIELD_LABELS.get(header, header))
        return headers


def main():
    root = tk.Tk()
    app = DatabaseSearchTool(root)
    root.mainloop()


if __name__ == "__main__":
    main()

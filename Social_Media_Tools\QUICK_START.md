# 🚀 دليل البدء السريع - CSV Cleaner Tool

## خطوات سريعة للاستخدام

### 1️⃣ تشغيل الأداة
```bash
# الطريقة الأولى - مباشرة
python csv_cleaner_tool.py

# الطريقة الثانية - باستخدام ملف bat
run_csv_cleaner.bat
```

### 2️⃣ الإعداد السريع
1. **اختر مجلد الإدخال** - المجلد الذي يحتوي على ملفات CSV
2. **اختر مجلد الإخراج** - مجلد حفظ الملفات المنظفة
3. **اترك الخيارات الافتراضية** (كلها مفعلة)
4. **اضغط "بدء التنظيف"**

### 3️⃣ ما تفعله الأداة تلقائياً

#### 🔧 توحيد رؤوس الأعمدة
```
قبل: ID, Name, Mail, Tel, Sex
بعد: facebook_id, first_name, email, phone, gender
```

#### 🧹 تنظيف البيانات
- إزالة المسافات الزائدة
- حذف الرموز الغريبة
- تصحيح علامات الاقتباس
- إصلاح مشاكل الترميز

#### 🗑️ إزالة المكررات
- كشف السجلات المتطابقة
- الاحتفاظ بنسخة واحدة فقط

#### ✂️ تقسيم الملفات الكبيرة
- تقسيم الملفات أكبر من مليون سجل
- تسمية تلقائية: `file_part_001.csv`

### 4️⃣ النتائج المتوقعة

#### قبل التنظيف:
```csv
ID,Name,Mail,Tel,Sex,Location
"100001234567890","أحمد محمد","<EMAIL>","01012345678","ذكر","القاهرة"
"100001234567890","أحمد محمد","<EMAIL>","01012345678","ذكر","القاهرة"
```

#### بعد التنظيف:
```csv
facebook_id,first_name,last_name,email,phone,gender,location
100001234567890,أحمد,محمد,<EMAIL>,01012345678,ذكر,القاهرة
```

### 5️⃣ مراقبة التقدم
- **شريط التقدم**: يظهر النسبة المئوية
- **الملف الحالي**: اسم الملف قيد المعالجة
- **الإحصائيات**: 
  - الملفات المعالجة
  - السجلات المعالجة
  - المكررات المحذوفة
  - الأخطاء المصححة

### 6️⃣ بعد الانتهاء
1. **رسالة نجاح** مع ملخص النتائج
2. **فتح مجلد النتائج** لرؤية الملفات المنظفة
3. **الملفات الجديدة** بأسماء `*_cleaned.csv`

## ⚡ نصائح للاستخدام الأمثل

### للملفات الصغيرة (أقل من 100MB):
- استخدم الإعدادات الافتراضية
- لا حاجة لتقسيم الملفات

### للملفات المتوسطة (100MB - 1GB):
- فعل تقسيم الملفات
- اجعل الحد 500,000 سجل لكل ملف

### للملفات الضخمة (أكبر من 1GB):
- فعل تقسيم الملفات
- اجعل الحد 250,000 سجل لكل ملف
- أغلق البرامج الأخرى
- تأكد من مساحة التخزين الكافية

## 🔧 حل المشاكل السريع

### المشكلة: "نفدت الذاكرة"
**الحل:**
1. قلل عدد السجلات لكل ملف إلى 100,000
2. أغلق البرامج الأخرى
3. أعد تشغيل الكمبيوتر

### المشكلة: "خطأ في فتح الملف"
**الحل:**
1. تأكد أن الملف بصيغة CSV
2. تأكد أن الملف غير مفتوح في برنامج آخر
3. تحقق من صلاحيات الوصول للمجلد

### المشكلة: "البرنامج لا يستجيب"
**الحل:**
1. انتظر - قد يكون يعالج ملف كبير
2. راقب استخدام الذاكرة في Task Manager
3. استخدم زر "إيقاف" إذا احتجت

## 📊 مثال عملي سريع

### 1. إنشاء مجلد اختبار
```
C:\test_csv\
├── input\          (ضع ملفات CSV هنا)
└── output\         (ستظهر النتائج هنا)
```

### 2. تشغيل الأداة
- اختر `C:\test_csv\input` كمجلد إدخال
- اختر `C:\test_csv\output` كمجلد إخراج
- اضغط "بدء التنظيف"

### 3. النتيجة
```
C:\test_csv\output\
├── file1_cleaned.csv
├── file2_part_001_cleaned.csv
└── file2_part_002_cleaned.csv
```

## 🎯 الهدف من الأداة

تحويل ملفات CSV غير منظمة مثل:
- ملف 13GB بـ 45 مليون سجل
- رؤوس أعمدة مختلفة
- بيانات مشوهة وغير نظيفة

إلى:
- ملفات منظمة ومقسمة
- رؤوس أعمدة موحدة
- بيانات نظيفة وجاهزة للبحث

**النتيجة**: بحث أسرع وأكثر كفاءة في البيانات الضخمة! 🚀

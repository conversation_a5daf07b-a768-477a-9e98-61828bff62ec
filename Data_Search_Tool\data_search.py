#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Data Search Tool - أداة البحث في البيانات
أداة متخصصة للبحث في قواعد البيانات الاجتماعية الضخمة

المميزات:
- البحث في ملفات TXT, CSV, DB
- فلترة متقدمة للنتائج
- دعم البيانات الضخمة (45+ مليون سجل)
- تصدير النتائج إلى CSV
- واجهة مستخدم سهلة
"""

import csv
import os
import sys
import time
import sqlite3
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread, Event
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class DataSearchApp:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🔍 Data Search Tool - أداة البحث في البيانات")
        self.root.geometry("1200x800")
        self.root.configure(pady=10, padx=10)

        # إنشاء متغير للتحكم في إيقاف المعالجة
        self.stop_event = Event()

        # تهيئة المتغيرات الأساسية
        self.init_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()

    def init_variables(self):
        """تهيئة جميع المتغيرات المطلوبة"""
        # المتغيرات الأساسية
        self.search_ids_file = tk.StringVar()
        self.output_path = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        
        # متغيرات البحث
        self.search_type = tk.StringVar(value="id")
        self.search_values = {}
        
        # قواعد البيانات المحددة
        self.selected_dbs = []
        
        # متغيرات المعالجة
        self.processing = False
        self.total_records = 0
        self.processed_records = 0
        self.total_matches = 0
        
        # حقول الإخراج
        self.output_fields = {}
        
        # معرفات البحث
        self.search_ids = set()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', pady=(0, 15))
        
        title_label = tk.Label(
            title_frame,
            text="🔍 Data Search Tool",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="أداة البحث في قواعد البيانات الاجتماعية الضخمة",
            font=('Arial', 12),
            fg='#7f8c8d'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # قسم البحث
        self.create_search_section()
        
        # قسم قواعد البيانات
        self.create_database_section()
        
        # قسم التحكم والتقدم
        self.create_control_section()

    def create_search_section(self):
        """إنشاء قسم البحث"""
        search_frame = ttk.LabelFrame(self.root, text="🔍 إعدادات البحث", padding=10)
        search_frame.pack(fill='x', pady=(0, 10))

        # ملف المعرفات
        file_frame = ttk.Frame(search_frame)
        file_frame.pack(fill='x', pady=5)
        ttk.Label(file_frame, text="📄 ملف المعرفات/الكلمات المفتاحية:").pack(anchor='w')
        
        entry_frame = ttk.Frame(file_frame)
        entry_frame.pack(fill='x', pady=2)
        ttk.Entry(entry_frame, textvariable=self.search_ids_file).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(entry_frame, text="تصفح", command=self.browse_search_file).pack(side='right')

        # نوع البحث
        type_frame = ttk.Frame(search_frame)
        type_frame.pack(fill='x', pady=5)
        ttk.Label(type_frame, text="🎯 نوع البحث:").pack(anchor='w')
        
        radio_frame = ttk.Frame(type_frame)
        radio_frame.pack(fill='x', pady=2)
        ttk.Radiobutton(radio_frame, text="📱 Facebook ID", 
                       variable=self.search_type, value="id").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(radio_frame, text="📞 رقم الهاتف", 
                       variable=self.search_type, value="phone").pack(side='left', padx=(0, 10))
        ttk.Radiobutton(radio_frame, text="📧 البريد الإلكتروني", 
                       variable=self.search_type, value="email").pack(side='left')

        # حقول البحث الإضافية
        fields_frame = ttk.LabelFrame(search_frame, text="🔎 بحث إضافي", padding=5)
        fields_frame.pack(fill='x', pady=(10, 0))
        
        # إنشاء حقول البحث
        self.create_search_fields(fields_frame)

    def create_search_fields(self, parent):
        """إنشاء حقول البحث الإضافية"""
        fields = [
            ('الاسم الأول', 'first_name'),
            ('اسم العائلة', 'last_name'),
            ('الجنس', 'gender'),
            ('الموقع', 'location'),
            ('العمل', 'work'),
            ('التعليم', 'education')
        ]

        # ترتيب الحقول في عمودين
        for i, (label, key) in enumerate(fields):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(parent, text=f"{label}:").grid(
                row=row, column=col, sticky='w', padx=(0, 5), pady=2)
            
            var = tk.StringVar()
            self.search_values[key] = var
            entry = ttk.Entry(parent, textvariable=var, width=20)
            entry.grid(row=row, column=col+1, sticky='ew', padx=(0, 10), pady=2)

        # تكوين الأعمدة
        parent.columnconfigure(1, weight=1)
        parent.columnconfigure(3, weight=1)

    def create_database_section(self):
        """إنشاء قسم قواعد البيانات"""
        db_frame = ttk.LabelFrame(self.root, text="🗄️ قواعد البيانات", padding=10)
        db_frame.pack(fill='both', expand=True, pady=(0, 10))

        # قائمة قواعد البيانات
        list_frame = ttk.Frame(db_frame)
        list_frame.pack(fill='both', expand=True, pady=(0, 10))

        self.db_listbox = tk.Listbox(
            list_frame,
            selectmode='multiple',
            height=6,
            font=('Arial', 10)
        )
        
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.db_listbox.yview)
        self.db_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.db_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # أزرار إدارة قواعد البيانات
        buttons_frame = ttk.Frame(db_frame)
        buttons_frame.pack(fill='x')

        ttk.Button(buttons_frame, text="➕ إضافة قاعدة بيانات",
                  command=self.add_database).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="➖ حذف",
                  command=self.remove_database).pack(side='left', padx=(0, 5))
        ttk.Button(buttons_frame, text="🗑️ مسح الكل",
                  command=self.clear_databases).pack(side='left')

    def create_control_section(self):
        """إنشاء قسم التحكم والتقدم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والنتائج", padding=10)
        control_frame.pack(fill='x')

        # ملف الإخراج
        output_frame = ttk.Frame(control_frame)
        output_frame.pack(fill='x', pady=(0, 10))
        ttk.Label(output_frame, text="💾 ملف النتائج:").pack(anchor='w')
        
        entry_frame = ttk.Frame(output_frame)
        entry_frame.pack(fill='x', pady=2)
        ttk.Entry(entry_frame, textvariable=self.output_path).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(entry_frame, text="تصفح", command=self.browse_output).pack(side='right')

        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))

        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء البحث",
                                      command=self.start_search, style='Accent.TButton')
        self.start_button.pack(side='left', padx=(0, 10))

        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف",
                                     command=self.stop_search, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))

        ttk.Button(buttons_frame, text="📁 فتح النتائج",
                  command=self.open_results).pack(side='left')

        # شريط التقدم
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill='x')

        ttk.Label(progress_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress,
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=2)

        # معلومات الحالة
        ttk.Label(progress_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(progress_frame, textvariable=self.status,
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_search_file(self):
        """تصفح ملف المعرفات"""
        filename = filedialog.askopenfilename(
            title="اختر ملف المعرفات",
            filetypes=[("ملفات نصية", "*.txt"), ("جميع الملفات", "*.*")]
        )
        if filename:
            self.search_ids_file.set(filename)
            self.load_search_ids()

    def load_search_ids(self):
        """تحميل معرفات البحث من الملف"""
        try:
            with open(self.search_ids_file.get(), 'r', encoding='utf-8') as f:
                self.search_ids = {line.strip() for line in f if line.strip()}
            self.status.set(f"تم تحميل {len(self.search_ids)} معرف")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة الملف:\n{str(e)}")
            self.search_ids = set()

    def browse_output(self):
        """تصفح ملف الإخراج"""
        filename = filedialog.asksaveasfilename(
            title="حفظ النتائج",
            defaultextension=".csv",
            filetypes=[("ملفات CSV", "*.csv"), ("جميع الملفات", "*.*")]
        )
        if filename:
            self.output_path.set(filename)

    def add_database(self):
        """إضافة قاعدة بيانات"""
        filetypes = [
            ("جميع الملفات المدعومة", "*.txt;*.csv;*.db"),
            ("ملفات نصية", "*.txt"),
            ("ملفات CSV", "*.csv"),
            ("قواعد بيانات SQLite", "*.db"),
            ("جميع الملفات", "*.*")
        ]

        filenames = filedialog.askopenfilenames(
            title="اختر قواعد البيانات",
            filetypes=filetypes
        )

        for filename in filenames:
            if filename not in self.selected_dbs:
                self.selected_dbs.append(filename)
                display_name = os.path.basename(filename)
                self.db_listbox.insert(tk.END, display_name)

        self.status.set(f"تم إضافة {len(filenames)} قاعدة بيانات")

    def remove_database(self):
        """حذف قاعدة البيانات المحددة"""
        selected_indices = self.db_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("تنبيه", "يرجى اختيار قاعدة بيانات لحذفها")
            return

        for index in reversed(selected_indices):
            self.db_listbox.delete(index)
            if index < len(self.selected_dbs):
                del self.selected_dbs[index]

    def clear_databases(self):
        """مسح جميع قواعد البيانات"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع قواعد البيانات؟"):
            self.db_listbox.delete(0, tk.END)
            self.selected_dbs.clear()

    def start_search(self):
        """بدء عملية البحث"""
        # التحقق من الإعدادات
        if not self.selected_dbs:
            messagebox.showwarning("تنبيه", "يرجى اختيار قاعدة بيانات واحدة على الأقل")
            return

        if not self.output_path.get():
            messagebox.showwarning("تنبيه", "يرجى تحديد ملف الإخراج")
            return

        # تحديث حالة الأزرار
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # بدء البحث
        self.processing = True
        self.stop_event.clear()
        Thread(target=self.search_process, daemon=True).start()

    def stop_search(self):
        """إيقاف عملية البحث"""
        self.processing = False
        self.stop_event.set()
        self.status.set("جاري إيقاف العملية...")

    def search_process(self):
        """عملية البحث الرئيسية"""
        try:
            self.total_matches = 0
            self.processed_records = 0

            with open(self.output_path.get(), 'w', newline='', encoding='utf-8') as outfile:
                writer = csv.writer(outfile)
                
                # كتابة رؤوس الأعمدة
                headers = ['facebook_id', 'first_name', 'last_name', 'email', 'phone', 
                          'gender', 'location', 'work', 'education', 'relationship']
                writer.writerow(headers)

                # معالجة كل قاعدة بيانات
                total_dbs = len(self.selected_dbs)
                for i, db_path in enumerate(self.selected_dbs):
                    if not self.processing:
                        break

                    self.status.set(f"معالجة قاعدة البيانات {i+1} من {total_dbs}")
                    self.process_database(db_path, writer)

                    # تحديث التقدم
                    progress = ((i + 1) / total_dbs) * 100
                    self.progress.set(progress)
                    self.root.update_idletasks()

            if self.processing:
                self.status.set(f"✅ اكتمل البحث! تم العثور على {self.total_matches} نتيجة")
                messagebox.showinfo("مكتمل", 
                    f"تم البحث بنجاح!\n\nالنتائج: {self.total_matches}\nالملف: {self.output_path.get()}")
            else:
                self.status.set("❌ تم إيقاف البحث")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث:\n{str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_database(self, db_path, writer):
        """معالجة قاعدة بيانات واحدة"""
        try:
            with open(db_path, 'r', encoding='utf-8', errors='ignore') as f:
                csv_reader = csv.reader(f)
                
                for row in csv_reader:
                    if not self.processing:
                        break

                    if self.match_criteria(row):
                        writer.writerow(row)
                        self.total_matches += 1

                    self.processed_records += 1

        except Exception as e:
            print(f"خطأ في معالجة {db_path}: {str(e)}")

    def match_criteria(self, row):
        """فحص مطابقة السجل لمعايير البحث"""
        if not row:
            return False

        # البحث بالمعرفات المحملة من الملف
        if self.search_ids and len(row) > 0:
            if row[0].strip().lower() in {id.lower() for id in self.search_ids}:
                return True

        # البحث بالحقول الإضافية
        for field, var in self.search_values.items():
            value = var.get().strip()
            if value:
                # بحث بسيط في جميع حقول السجل
                for cell in row:
                    if value.lower() in str(cell).lower():
                        return True

        return False

    def open_results(self):
        """فتح ملف النتائج"""
        if self.output_path.get() and os.path.exists(self.output_path.get()):
            folder_path = os.path.dirname(self.output_path.get())
            os.startfile(folder_path)
        else:
            messagebox.showinfo("معلومات", "لا يوجد ملف نتائج")


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    
    # تحسين المظهر
    try:
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Accent.TButton', 
                       background='#0078d4', 
                       foreground='white',
                       font=('Arial', 10, 'bold'))
    except Exception:
        pass
    
    app = DataSearchApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()

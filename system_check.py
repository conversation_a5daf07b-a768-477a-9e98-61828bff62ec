#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 System Check Tool
أداة فحص النظام والتأكد من جاهزية الأدوات

تتحقق من:
- إصدار Python
- وجود الملفات المطلوبة
- صلاحيات الوصول
- مساحة التخزين
- الذاكرة المتاحة
"""

import sys
import os
import platform
import shutil
import psutil
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox

class SystemChecker:
    def __init__(self, root):
        """تهيئة أداة فحص النظام"""
        self.root = root
        self.root.title("🔧 System Check - فحص النظام")
        self.root.geometry("700x600")
        self.root.configure(pady=15, padx=15)
        
        # المتغيرات
        self.current_dir = Path(__file__).parent
        self.required_files = [
            'main_launcher.py',
            'csv_cleaner_tool.py',
            'txt_to_csv_converter.py'
        ]
        
        self.batch_files = [
            '🚀 تشغيل الأدوات.bat',
            '🧹 تنظيف CSV.bat',
            '🔍 البحث في البيانات.bat'
        ]
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تشغيل الفحص تلقائياً
        self.root.after(1000, self.run_full_check)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', pady=(0, 15))
        
        title_label = tk.Label(
            title_frame,
            text="🔧 System Check - فحص النظام",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="التحقق من جاهزية النظام وتوفر جميع المتطلبات",
            font=('Arial', 10),
            fg='#7f8c8d'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # منطقة النتائج
        self.results_frame = ttk.LabelFrame(self.root, text="📊 نتائج الفحص", padding=10)
        self.results_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # منطقة التحكم
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill='x')
        
        ttk.Button(control_frame, text="🔄 إعادة الفحص", 
                  command=self.run_full_check).pack(side='left', padx=(0, 10))
        
        ttk.Button(control_frame, text="🚀 تشغيل المنصة الرئيسية", 
                  command=self.launch_main_app).pack(side='left', padx=(0, 10))
        
        ttk.Button(control_frame, text="❌ إغلاق", 
                  command=self.root.quit).pack(side='right')

    def clear_results(self):
        """مسح النتائج السابقة"""
        for widget in self.results_frame.winfo_children():
            widget.destroy()

    def add_result(self, text, status="info", details=None):
        """إضافة نتيجة فحص"""
        # تحديد الأيقونة واللون
        if status == "success":
            icon = "✅"
            color = "green"
        elif status == "error":
            icon = "❌"
            color = "red"
        elif status == "warning":
            icon = "⚠️"
            color = "orange"
        else:
            icon = "ℹ️"
            color = "blue"
        
        # إطار النتيجة
        result_frame = ttk.Frame(self.results_frame)
        result_frame.pack(fill='x', pady=2)
        
        # النص الرئيسي
        main_label = tk.Label(
            result_frame,
            text=f"{icon} {text}",
            font=('Arial', 10),
            fg=color,
            anchor='w'
        )
        main_label.pack(anchor='w')
        
        # التفاصيل (إن وجدت)
        if details:
            details_label = tk.Label(
                result_frame,
                text=f"   {details}",
                font=('Arial', 9),
                fg='gray',
                anchor='w'
            )
            details_label.pack(anchor='w')

    def check_python_version(self):
        """فحص إصدار Python"""
        try:
            version = sys.version_info
            version_str = f"{version.major}.{version.minor}.{version.micro}"
            
            if version >= (3, 6):
                self.add_result(
                    f"إصدار Python: {version_str}",
                    "success",
                    "الإصدار مدعوم ومناسب"
                )
                return True
            else:
                self.add_result(
                    f"إصدار Python: {version_str}",
                    "error",
                    "يتطلب Python 3.6 أو أحدث"
                )
                return False
        except Exception as e:
            self.add_result("فشل في فحص إصدار Python", "error", str(e))
            return False

    def check_system_info(self):
        """فحص معلومات النظام"""
        try:
            # نظام التشغيل
            os_name = platform.system()
            os_version = platform.release()
            self.add_result(
                f"نظام التشغيل: {os_name} {os_version}",
                "info"
            )
            
            # معمارية النظام
            architecture = platform.machine()
            self.add_result(
                f"معمارية النظام: {architecture}",
                "info"
            )
            
            return True
        except Exception as e:
            self.add_result("فشل في فحص معلومات النظام", "error", str(e))
            return False

    def check_memory(self):
        """فحص الذاكرة المتاحة"""
        try:
            memory = psutil.virtual_memory()
            total_gb = memory.total / (1024**3)
            available_gb = memory.available / (1024**3)
            used_percent = memory.percent
            
            if available_gb >= 2:
                status = "success"
                details = f"متاح: {available_gb:.1f}GB من {total_gb:.1f}GB (استخدام: {used_percent:.1f}%)"
            elif available_gb >= 1:
                status = "warning"
                details = f"متاح: {available_gb:.1f}GB من {total_gb:.1f}GB (قد تحتاج إغلاق برامج أخرى)"
            else:
                status = "error"
                details = f"متاح: {available_gb:.1f}GB فقط (غير كافي للبيانات الكبيرة)"
            
            self.add_result("الذاكرة المتاحة", status, details)
            return status != "error"
            
        except Exception as e:
            self.add_result("فشل في فحص الذاكرة", "error", str(e))
            return False

    def check_disk_space(self):
        """فحص مساحة التخزين"""
        try:
            disk_usage = shutil.disk_usage(self.current_dir)
            free_gb = disk_usage.free / (1024**3)
            total_gb = disk_usage.total / (1024**3)
            used_percent = ((disk_usage.total - disk_usage.free) / disk_usage.total) * 100
            
            if free_gb >= 5:
                status = "success"
                details = f"متاح: {free_gb:.1f}GB من {total_gb:.1f}GB"
            elif free_gb >= 1:
                status = "warning"
                details = f"متاح: {free_gb:.1f}GB (قد لا تكفي للبيانات الضخمة)"
            else:
                status = "error"
                details = f"متاح: {free_gb:.1f}GB فقط (غير كافي)"
            
            self.add_result("مساحة التخزين", status, details)
            return status != "error"
            
        except Exception as e:
            self.add_result("فشل في فحص مساحة التخزين", "error", str(e))
            return False

    def check_required_files(self):
        """فحص وجود الملفات المطلوبة"""
        all_found = True
        
        # فحص ملفات Python
        for file_name in self.required_files:
            file_path = self.current_dir / file_name
            if file_path.exists():
                file_size = file_path.stat().st_size / 1024  # KB
                self.add_result(
                    f"ملف Python: {file_name}",
                    "success",
                    f"الحجم: {file_size:.1f}KB"
                )
            else:
                self.add_result(
                    f"ملف Python: {file_name}",
                    "error",
                    "الملف غير موجود"
                )
                all_found = False
        
        # فحص ملفات التشغيل
        for file_name in self.batch_files:
            file_path = self.current_dir / file_name
            if file_path.exists():
                self.add_result(
                    f"ملف تشغيل: {file_name}",
                    "success"
                )
            else:
                self.add_result(
                    f"ملف تشغيل: {file_name}",
                    "warning",
                    "الملف غير موجود (يمكن تشغيل Python مباشرة)"
                )
        
        return all_found

    def check_file_permissions(self):
        """فحص صلاحيات الوصول للملفات"""
        try:
            # فحص صلاحية القراءة والكتابة في المجلد
            test_file = self.current_dir / "test_permissions.tmp"
            
            # محاولة إنشاء ملف اختبار
            with open(test_file, 'w') as f:
                f.write("test")
            
            # محاولة قراءة الملف
            with open(test_file, 'r') as f:
                content = f.read()
            
            # حذف ملف الاختبار
            test_file.unlink()
            
            self.add_result(
                "صلاحيات الوصول",
                "success",
                "يمكن القراءة والكتابة في المجلد"
            )
            return True
            
        except Exception as e:
            self.add_result(
                "صلاحيات الوصول",
                "error",
                f"لا يمكن الكتابة في المجلد: {str(e)}"
            )
            return False

    def run_full_check(self):
        """تشغيل فحص شامل للنظام"""
        self.clear_results()
        
        # عرض رسالة بدء الفحص
        self.add_result("بدء فحص النظام...", "info")
        self.root.update()
        
        # تشغيل جميع الفحوصات
        checks = [
            ("فحص إصدار Python", self.check_python_version),
            ("فحص معلومات النظام", self.check_system_info),
            ("فحص الذاكرة", self.check_memory),
            ("فحص مساحة التخزين", self.check_disk_space),
            ("فحص الملفات المطلوبة", self.check_required_files),
            ("فحص صلاحيات الوصول", self.check_file_permissions)
        ]
        
        results = []
        for check_name, check_func in checks:
            try:
                result = check_func()
                results.append(result)
            except Exception as e:
                self.add_result(f"خطأ في {check_name}", "error", str(e))
                results.append(False)
        
        # ملخص النتائج
        self.add_result("", "info")  # سطر فارغ
        
        success_count = sum(results)
        total_count = len(results)
        
        if success_count == total_count:
            self.add_result(
                "✅ النظام جاهز تماماً!",
                "success",
                "يمكنك تشغيل جميع الأدوات بأمان"
            )
        elif success_count >= total_count - 1:
            self.add_result(
                "⚠️ النظام جاهز مع تحذيرات",
                "warning",
                "يمكن تشغيل الأدوات مع مراعاة التحذيرات"
            )
        else:
            self.add_result(
                "❌ النظام غير جاهز",
                "error",
                "يرجى حل المشاكل المذكورة أعلاه"
            )

    def launch_main_app(self):
        """تشغيل المنصة الرئيسية"""
        try:
            main_file = self.current_dir / "main_launcher.py"
            if main_file.exists():
                import subprocess
                subprocess.Popen([sys.executable, str(main_file)])
                messagebox.showinfo("تم التشغيل", "تم تشغيل المنصة الرئيسية بنجاح!")
            else:
                messagebox.showerror("خطأ", "ملف المنصة الرئيسية غير موجود")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل المنصة الرئيسية:\n{str(e)}")


def main():
    """تشغيل أداة فحص النظام"""
    root = tk.Tk()
    
    # تحسين مظهر التطبيق
    try:
        style = ttk.Style()
        style.theme_use('clam')
    except Exception:
        pass
    
    app = SystemChecker(root)
    root.mainloop()


if __name__ == "__main__":
    main()

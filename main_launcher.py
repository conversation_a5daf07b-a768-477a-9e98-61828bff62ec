#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Main Launcher - Social Media Data Tools
منصة الأدوات الرئيسية للبيانات الاجتماعية

يجمع جميع الأدوات في واجهة واحدة سهلة الاستخدام
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
from pathlib import Path

class MainLauncher:
    def __init__(self, root):
        """تهيئة المنصة الرئيسية"""
        self.root = root
        self.root.title("🚀 Social Media Data Tools - أدوات البيانات الاجتماعية")
        self.root.geometry("800x600")
        self.root.configure(pady=20, padx=20)
        
        # تحديد مسار المجلد الحالي
        self.current_dir = Path(__file__).parent
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحديد الأدوات المتاحة
        self.tools = {
            'csv_cleaner': {
                'name': '🧹 CSV Cleaner Tool',
                'description': 'أداة تنظيف وتنسيق ملفات CSV',
                'file': 'csv_cleaner_tool.py',
                'details': 'تنظيف البيانات، توحيد الرؤوس، إزالة المكررات، تقسيم الملفات الكبيرة'
            },
            'search_tool': {
                'name': '🔍 Data Search Tool',
                'description': 'أداة البحث في البيانات الاجتماعية',
                'file': 'txt_to_csv_converter.py',
                'details': 'البحث في قواعد البيانات الضخمة، فلترة النتائج، تصدير CSV'
            }
        }

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        self.create_header()
        
        # قسم الأدوات
        self.create_tools_section()
        
        # قسم المعلومات
        self.create_info_section()
        
        # قسم الإعدادات
        self.create_settings_section()

    def create_header(self):
        """إنشاء رأس التطبيق"""
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill='x', pady=(0, 20))
        
        # العنوان
        title_label = tk.Label(
            header_frame,
            text="🚀 Social Media Data Tools",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack()
        
        # العنوان الفرعي
        subtitle_label = tk.Label(
            header_frame,
            text="منصة شاملة لمعالجة والبحث في البيانات الاجتماعية",
            font=('Arial', 12),
            fg='#7f8c8d'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # خط فاصل
        separator = ttk.Separator(header_frame, orient='horizontal')
        separator.pack(fill='x', pady=(15, 0))

    def create_tools_section(self):
        """إنشاء قسم الأدوات"""
        tools_frame = ttk.LabelFrame(self.root, text="🛠️ الأدوات المتاحة", padding=15)
        tools_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # أداة تنظيف CSV
        self.create_tool_card(
            tools_frame,
            "🧹 CSV Cleaner Tool",
            "أداة تنظيف وتنسيق ملفات CSV",
            "• تنظيف البيانات وإزالة الرموز الغريبة\n• توحيد رؤوس الأعمدة\n• إزالة السجلات المكررة\n• تقسيم الملفات الكبيرة",
            lambda: self.launch_tool('csv_cleaner_tool.py'),
            row=0
        )
        
        # أداة البحث
        self.create_tool_card(
            tools_frame,
            "🔍 Data Search Tool",
            "أداة البحث في البيانات الاجتماعية",
            "• البحث في قواعد البيانات الضخمة\n• فلترة متقدمة للنتائج\n• دعم ملفات TXT, CSV, DB\n• تصدير النتائج إلى CSV",
            lambda: self.launch_tool('txt_to_csv_converter.py'),
            row=1
        )

    def create_tool_card(self, parent, title, description, features, command, row):
        """إنشاء بطاقة أداة"""
        # إطار البطاقة
        card_frame = ttk.Frame(parent)
        card_frame.pack(fill='x', pady=10)
        
        # إطار المحتوى
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(side='left', fill='both', expand=True)
        
        # العنوان
        title_label = tk.Label(
            content_frame,
            text=title,
            font=('Arial', 14, 'bold'),
            fg='#2980b9'
        )
        title_label.pack(anchor='w')
        
        # الوصف
        desc_label = tk.Label(
            content_frame,
            text=description,
            font=('Arial', 10),
            fg='#34495e'
        )
        desc_label.pack(anchor='w', pady=(2, 5))
        
        # المميزات
        features_label = tk.Label(
            content_frame,
            text=features,
            font=('Arial', 9),
            fg='#7f8c8d',
            justify='left'
        )
        features_label.pack(anchor='w')
        
        # زر التشغيل
        launch_button = ttk.Button(
            card_frame,
            text="🚀 تشغيل",
            command=command,
            style='Accent.TButton'
        )
        launch_button.pack(side='right', padx=(10, 0))
        
        # خط فاصل
        if row < 1:  # ليس آخر عنصر
            separator = ttk.Separator(parent, orient='horizontal')
            separator.pack(fill='x', pady=(10, 0))

    def create_info_section(self):
        """إنشاء قسم المعلومات"""
        info_frame = ttk.LabelFrame(self.root, text="ℹ️ معلومات النظام", padding=10)
        info_frame.pack(fill='x', pady=(0, 15))
        
        # معلومات Python
        python_version = f"Python {sys.version.split()[0]}"
        python_label = tk.Label(info_frame, text=f"🐍 إصدار Python: {python_version}", font=('Arial', 9))
        python_label.pack(anchor='w')
        
        # مسار العمل
        work_dir = str(self.current_dir)
        dir_label = tk.Label(info_frame, text=f"📁 مجلد العمل: {work_dir}", font=('Arial', 9))
        dir_label.pack(anchor='w', pady=(2, 0))
        
        # حالة الأدوات
        self.check_tools_status(info_frame)

    def check_tools_status(self, parent):
        """فحص حالة الأدوات"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill='x', pady=(5, 0))
        
        tk.Label(status_frame, text="🔧 حالة الأدوات:", font=('Arial', 9, 'bold')).pack(anchor='w')
        
        for tool_key, tool_info in self.tools.items():
            file_path = self.current_dir / tool_info['file']
            status = "✅ متاح" if file_path.exists() else "❌ غير موجود"
            color = "green" if file_path.exists() else "red"
            
            tool_status = tk.Label(
                status_frame,
                text=f"  • {tool_info['name']}: {status}",
                font=('Arial', 8),
                fg=color
            )
            tool_status.pack(anchor='w')

    def create_settings_section(self):
        """إنشاء قسم الإعدادات والتحكم"""
        settings_frame = ttk.LabelFrame(self.root, text="⚙️ إعدادات وتحكم", padding=10)
        settings_frame.pack(fill='x')
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(settings_frame)
        buttons_frame.pack(fill='x')
        
        # زر فتح مجلد العمل
        ttk.Button(
            buttons_frame,
            text="📁 فتح مجلد العمل",
            command=self.open_work_folder
        ).pack(side='left', padx=(0, 10))
        
        # زر تحديث
        ttk.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.refresh_status
        ).pack(side='left', padx=(0, 10))
        
        # زر المساعدة
        ttk.Button(
            buttons_frame,
            text="❓ مساعدة",
            command=self.show_help
        ).pack(side='left', padx=(0, 10))
        
        # زر الخروج
        ttk.Button(
            buttons_frame,
            text="❌ خروج",
            command=self.root.quit
        ).pack(side='right')

    def launch_tool(self, tool_file):
        """تشغيل أداة معينة"""
        tool_path = self.current_dir / tool_file
        
        if not tool_path.exists():
            messagebox.showerror(
                "خطأ",
                f"الملف غير موجود: {tool_file}\n\nتأكد من وجود الملف في نفس مجلد هذا التطبيق."
            )
            return
        
        try:
            # تشغيل الأداة في عملية منفصلة
            subprocess.Popen([sys.executable, str(tool_path)], cwd=str(self.current_dir))
            
            # رسالة تأكيد
            messagebox.showinfo(
                "تم التشغيل",
                f"تم تشغيل {tool_file} بنجاح!\n\nستظهر نافذة الأداة خلال ثوانٍ قليلة."
            )
            
        except Exception as e:
            messagebox.showerror(
                "خطأ في التشغيل",
                f"فشل في تشغيل {tool_file}\n\nالخطأ: {str(e)}"
            )

    def open_work_folder(self):
        """فتح مجلد العمل"""
        try:
            if sys.platform == "win32":
                os.startfile(str(self.current_dir))
            elif sys.platform == "darwin":  # macOS
                subprocess.run(["open", str(self.current_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(self.current_dir)])
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح المجلد:\n{str(e)}")

    def refresh_status(self):
        """تحديث حالة التطبيق"""
        # إعادة إنشاء الواجهة
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.setup_ui()
        
        messagebox.showinfo("تم التحديث", "تم تحديث حالة التطبيق بنجاح!")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🚀 دليل الاستخدام السريع

📋 الأدوات المتاحة:

🧹 CSV Cleaner Tool:
• لتنظيف وتنسيق ملفات CSV الكبيرة
• يحل مشكلة البيانات غير المنظمة
• يوحد رؤوس الأعمدة ويقسم الملفات الضخمة

🔍 Data Search Tool:
• للبحث في قواعد البيانات الاجتماعية
• يدعم ملفات TXT, CSV, DB
• بحث متقدم وتصدير النتائج

💡 نصائح:
• استخدم CSV Cleaner أولاً لتنظيف البيانات
• ثم استخدم Search Tool للبحث في البيانات المنظفة
• احتفظ بنسخة احتياطية من بياناتك

❓ للمساعدة الإضافية:
راجع ملفات README في مجلد العمل
        """
        
        messagebox.showinfo("المساعدة", help_text)


def main():
    """تشغيل التطبيق الرئيسي"""
    root = tk.Tk()
    
    # تحسين مظهر التطبيق
    try:
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص الألوان
        style.configure('Accent.TButton', 
                       background='#3498db', 
                       foreground='white',
                       font=('Arial', 10, 'bold'))
        
        style.map('Accent.TButton',
                 background=[('active', '#2980b9')])
        
    except Exception:
        pass  # استخدام النمط الافتراضي
    
    # إنشاء التطبيق
    app = MainLauncher(root)
    
    # تشغيل التطبيق
    root.mainloop()


if __name__ == "__main__":
    main()

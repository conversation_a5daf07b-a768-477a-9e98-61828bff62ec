# 🔍 Data Search Tool - أداة البحث في البيانات

## 📋 الوصف
أداة متخصصة للبحث في قواعد البيانات الاجتماعية الضخمة. تدعم البحث في ملفات TXT, CSV, DB وتتعامل مع البيانات الكبيرة بكفاءة عالية.

## 🚀 التشغيل
**دبل كليك على**: `🔍 تشغيل أداة البحث.bat`

## ✨ المميزات

### 🔍 بحث متقدم
- البحث بمعرفات Facebook
- البحث بأرقام الهواتف
- البحث بالبريد الإلكتروني
- البحث بالأسماء والمواقع

### 🗄️ دعم قواعد بيانات متعددة
- ملفات TXT
- ملفات CSV
- قواعد بيانات SQLite (.db)
- معالجة ملفات ضخمة (45+ مليون سجل)

### ⚡ أداء عالي
- معالجة batch للبيانات الكبيرة
- بحث سريع ومحسن
- استهلاك ذاكرة منخفض
- شريط تقدم مفصل

### 📊 تصدير النتائج
- تصدير إلى CSV
- رؤوس أعمدة موحدة
- بيانات منظمة وجاهزة للاستخدام

## 📁 كيفية الاستخدام

### 1️⃣ تشغيل الأداة
دبل كليك على `🔍 تشغيل أداة البحث.bat`

### 2️⃣ إعداد البحث
- **ملف المعرفات**: اختر ملف .txt يحتوي على المعرفات (معرف واحد في كل سطر)
- **نوع البحث**: اختر Facebook ID، رقم الهاتف، أو البريد الإلكتروني
- **بحث إضافي**: أدخل قيم للبحث في الأسماء، المواقع، إلخ

### 3️⃣ إضافة قواعد البيانات
- اضغط "➕ إضافة قاعدة بيانات"
- اختر ملفات TXT, CSV, أو DB
- يمكن إضافة عدة ملفات في نفس الوقت

### 4️⃣ تحديد ملف النتائج
- اختر مكان حفظ النتائج (ملف CSV)

### 5️⃣ بدء البحث
- اضغط "🚀 بدء البحث"
- راقب التقدم في الشريط
- انتظر اكتمال العملية

## 📄 تنسيق ملف المعرفات

### مثال على ملف المعرفات (search_ids.txt):
```
100001234567890
100001234567891
100001234567892
<EMAIL>
01012345678
```

### ملاحظات:
- معرف واحد في كل سطر
- يمكن خلط أنواع مختلفة من المعرفات
- تجاهل الأسطر الفارغة تلقائياً

## 🎯 أنواع البحث المدعومة

### 📱 Facebook ID
- البحث بمعرفات Facebook الرقمية
- مثال: `100001234567890`

### 📞 رقم الهاتف
- البحث بأرقام الهواتف
- مثال: `01012345678`, `+201012345678`

### 📧 البريد الإلكتروني
- البحث بعناوين البريد الإلكتروني
- مثال: `<EMAIL>`

### 🔎 بحث إضافي
- **الاسم الأول**: البحث في أسماء الأشخاص
- **اسم العائلة**: البحث في أسماء العائلات
- **الجنس**: ذكر، أنثى، Male, Female
- **الموقع**: المدن، البلدان
- **العمل**: المهن، الشركات
- **التعليم**: الجامعات، المدارس

## 📊 تنسيق النتائج

### رؤوس الأعمدة في ملف النتائج:
```csv
facebook_id,first_name,last_name,email,phone,gender,location,work,education,relationship
```

### مثال على النتائج:
```csv
facebook_id,first_name,last_name,email,phone,gender,location,work,education,relationship
100001234567890,أحمد,محمد,<EMAIL>,01012345678,ذكر,القاهرة,مهندس,جامعة القاهرة,متزوج
100001234567891,فاطمة,علي,<EMAIL>,01098765432,أنثى,الإسكندرية,طبيبة,جامعة الإسكندرية,عازبة
```

## 🔧 متطلبات النظام
- **Python 3.6+** (مطلوب)
- **4GB RAM** (8GB+ للبيانات الكبيرة)
- **مساحة تخزين**: للنتائج المصدرة

## 💡 نصائح للاستخدام الأمثل

### للبيانات الضخمة:
1. استخدم بيانات منظفة من أداة تنظيف CSV
2. أغلق البرامج الأخرى أثناء البحث
3. تأكد من مساحة التخزين الكافية

### لتحسين الأداء:
1. ضع قواعد البيانات على SSD
2. استخدم ملفات معرفات صغيرة (أقل من 100,000 معرف)
3. قسم البحث إلى دفعات إذا كانت النتائج كبيرة

### لنتائج أفضل:
1. استخدم معرفات دقيقة
2. جرب أنواع بحث مختلفة
3. استخدم البحث الإضافي للتصفية

## 🛠️ حل المشاكل

### "لا توجد نتائج"
1. تحقق من صحة المعرفات
2. جرب نوع بحث مختلف
3. تأكد من تنسيق قاعدة البيانات

### "البحث بطيء"
1. استخدم بيانات منظفة
2. قلل عدد المعرفات
3. أغلق البرامج الأخرى

### "خطأ في قراءة الملف"
1. تحقق من تنسيق الملف
2. تأكد من الترميز (UTF-8)
3. جرب ملف أصغر للاختبار

## 📈 النتائج المتوقعة
- بحث سريع في البيانات الضخمة
- نتائج دقيقة ومنظمة
- تصدير سهل للنتائج
- توفير الوقت والجهد

---
**هذه الأداة تجعل البحث في البيانات الضخمة سريع وسهل! 🚀**

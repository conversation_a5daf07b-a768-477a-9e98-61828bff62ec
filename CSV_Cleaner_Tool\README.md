# 🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV

## 📋 الوصف
أداة متخصصة لتنظيف وتحسين ملفات CSV الكبيرة، خاصة البيانات الاجتماعية. تحل مشكلة البيانات غير المنظمة وتوحد تنسيق جميع الملفات.

## 🚀 التشغيل
**دبل كليك على**: `🧹 تشغيل أداة تنظيف CSV.bat`

## ✨ المميزات

### 🔧 تنظيف البيانات
- إزالة المسافات الزائدة
- تنظيف الرموز الغريبة
- إصلاح مشاكل الترميز
- تنظيف علامات الاقتباس

### 📊 توحيد التنسيق
- رؤوس أعمدة موحدة
- تحويل أسماء الحقول للتنسيق القياسي
- ترتيب البيانات

### 🗑️ إزالة المكررات
- كشف السجلات المتطابقة
- الاحتفاظ بنسخة واحدة فقط

### ✂️ تقسيم الملفات الكبيرة
- تقسيم الملفات أكبر من مليون سجل
- تسمية تلقائية للأجزاء

## 🎯 رؤوس الأعمدة الموحدة
```
facebook_id      - معرف Facebook
first_name       - الاسم الأول
last_name        - اسم العائلة
email           - البريد الإلكتروني
phone           - رقم الهاتف
gender          - الجنس
birthday        - تاريخ الميلاد
location        - الموقع
work            - العمل
education       - التعليم
relationship    - الحالة الاجتماعية
```

## 📁 كيفية الاستخدام

### 1️⃣ تشغيل الأداة
دبل كليك على `🧹 تشغيل أداة تنظيف CSV.bat`

### 2️⃣ اختيار المجلدات
- **مجلد الإدخال**: المجلد الذي يحتوي على ملفات CSV
- **مجلد الإخراج**: مجلد حفظ الملفات المنظفة

### 3️⃣ تحديد الخيارات
- ✅ توحيد رؤوس الأعمدة (موصى به)
- ✅ تنظيف البيانات (موصى به)
- ✅ إصلاح الترميز (موصى به)
- ✅ إزالة المكررات (موصى به)
- ✅ تقسيم الملفات الكبيرة (للملفات أكبر من 1GB)

### 4️⃣ بدء التنظيف
اضغط "🚀 بدء التنظيف" وانتظر اكتمال العملية

## 🔄 مثال على التحويل

### قبل التنظيف:
```csv
ID,Name,Mail,Tel,Sex
"100001234567890","أحمد محمد","<EMAIL>","01012345678","ذكر"
"100001234567890","أحمد محمد","<EMAIL>","01012345678","ذكر"
```

### بعد التنظيف:
```csv
facebook_id,first_name,last_name,email,phone,gender
100001234567890,أحمد,محمد,<EMAIL>,01012345678,ذكر
```

## 🔧 متطلبات النظام
- **Python 3.6+** (مطلوب)
- **4GB RAM** (8GB+ للملفات الكبيرة)
- **مساحة تخزين**: ضعف حجم الملفات الأصلية

## 💡 نصائح للاستخدام الأمثل

### للملفات الكبيرة (أكبر من 1GB):
1. فعل تقسيم الملفات
2. اجعل الحد 500,000 سجل لكل ملف
3. أغلق البرامج الأخرى

### لتحسين الأداء:
1. ضع الملفات على SSD
2. تأكد من مساحة التخزين الكافية
3. احتفظ بنسخة احتياطية

## 🛠️ حل المشاكل

### "Python غير مثبت"
1. حمل من [python.org](https://python.org)
2. تأكد من "Add to PATH"
3. أعد تشغيل الكمبيوتر

### "نفدت الذاكرة"
1. قلل عدد السجلات لكل ملف
2. أغلق البرامج الأخرى
3. أعد تشغيل الكمبيوتر

## 📈 النتائج المتوقعة
- ملفات CSV منظمة ونظيفة
- رؤوس أعمدة موحدة
- بيانات جاهزة للبحث السريع
- تحسن كبير في أداء البحث

---
**هذه الأداة تحضر بياناتك للبحث السريع والفعال! 🚀**

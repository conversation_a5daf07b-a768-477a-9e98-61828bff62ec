#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗑️ المرحلة 1.5: حذف الأعمدة x من البيانات النظيفة
STEP 1.5: Remove X Columns from Clean Data
"""

import os
import csv
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
from pathlib import Path

class XColumnRemover:
    def __init__(self, root):
        """تهيئة أداة حذف الأعمدة x"""
        self.root = root
        self.root.title("🗑️ المرحلة 1.5: حذف الأعمدة x")
        self.root.geometry("800x600")
        self.root.configure(pady=10, padx=10)
        
        # الفاصل البديل
        self.PIPE_DELIMITER = "|"
        
        # النسق الأصلي مع الأعمدة x (26 عمود)
        self.ORIGINAL_HEADERS = [
            'facebook_id', 'x', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'x', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 'country',
            'education', 'user', 'x', 'x', 'x', 'x', 'x', 'status'
        ]
        
        # النسق النهائي بعد حذف الأعمدة x (19 عمود)
        self.FINAL_HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # مؤشرات الأعمدة المطلوبة (بدون x)
        self.KEEP_COLUMNS = [0, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 25]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.processing = False
        
        # خيارات الإخراج
        self.output_format = tk.StringVar(value="txt")  # txt أو csv
        
        self.setup_ui()

    def setup_ui(self):
        """إعداد الواجهة"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🗑️ المرحلة 1.5: حذف الأعمدة x",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # معلومات
        info_frame = ttk.LabelFrame(self.root, text="ℹ️ معلومات", padding=10)
        info_frame.pack(fill='x', pady=(0, 10))
        
        info_text = f"هذه المرحلة تحذف الأعمدة x وتحتفظ بـ 19 عمود فقط\nالفاصل: {self.PIPE_DELIMITER}"
        ttk.Label(info_frame, text=info_text).pack(anchor='w')
        
        # المجلدات
        self.create_folder_section()
        
        # الخيارات
        self.create_options_section()
        
        # التحكم
        self.create_control_section()
        
        # النتائج
        self.create_results_section()

    def create_folder_section(self):
        """قسم المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد البيانات مع الأعمدة x:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد البيانات النظيفة (بدون x):").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات المعالجة", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # تنسيق الإخراج
        ttk.Label(options_frame, text="💾 تنسيق الملفات النظيفة:").pack(anchor='w')
        
        format_frame = ttk.Frame(options_frame)
        format_frame.pack(fill='x', pady=5)
        
        ttk.Radiobutton(format_frame, text="📝 TXT مع | (أسرع للإدراج)", 
                       variable=self.output_format, value="txt").pack(side='left', padx=(0, 20))
        ttk.Radiobutton(format_frame, text="📈 CSV مع | (متوافق أكثر)", 
                       variable=self.output_format, value="csv").pack(side='left')

    def create_control_section(self):
        """قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🗑️ حذف الأعمدة x", 
                                      command=self.start_processing)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_processing, state='disabled')
        self.stop_button.pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # الحالة
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def create_results_section(self):
        """قسم النتائج"""
        results_frame = ttk.LabelFrame(self.root, text="📊 نتائج المعالجة", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log(self, message):
        """إضافة رسالة للنتائج"""
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد البيانات مع الأعمدة x")
        if folder:
            self.input_folder.set(folder)
            files = list(Path(folder).glob("*.*"))
            self.status.set(f"تم اختيار المجلد - وجد {len(files)} ملف")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد البيانات النظيفة")
        if folder:
            self.output_folder.set(folder)

    def start_processing(self):
        """بدء المعالجة"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.processing_thread, daemon=True).start()

    def stop_processing(self):
        """إيقاف المعالجة"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def processing_thread(self):
        """معالجة حذف الأعمدة x"""
        try:
            self.log("🗑️ بدء حذف الأعمدة x...")
            self.log(f"📊 الأعمدة المحتفظ بها: {len(self.FINAL_HEADERS)}")
            
            input_path = Path(self.input_folder.get())
            files = list(input_path.glob("*.csv")) + list(input_path.glob("*.txt"))
            
            if not files:
                self.log("❌ لا توجد ملفات للمعالجة")
                return
            
            self.log(f"📊 عدد الملفات: {len(files)}")
            
            total_records = 0
            
            for i, file_path in enumerate(files):
                if not self.processing:
                    break
                
                self.log(f"\n📄 معالجة الملف {i+1}/{len(files)}: {file_path.name}")
                self.status.set(f"معالجة {file_path.name}")
                self.progress.set((i / len(files)) * 100)
                
                records = self.process_single_file(file_path)
                total_records += records
                
                self.root.update_idletasks()
            
            if self.processing:
                self.log(f"\n🎉 تم الانتهاء من حذف الأعمدة x!")
                self.log(f"📊 إجمالي السجلات المعالجة: {total_records}")
                self.log(f"📊 عدد الأعمدة النهائي: {len(self.FINAL_HEADERS)}")
                self.status.set("✅ تم الانتهاء!")
                messagebox.showinfo("مكتمل", f"تم معالجة {total_records} سجل بنجاح!")
            
        except Exception as e:
            self.log(f"❌ خطأ عام: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_single_file(self, file_path):
        """معالجة ملف واحد"""
        try:
            # قراءة الملف
            if file_path.suffix.lower() == '.csv':
                data = self.read_csv_file(file_path)
            else:
                data = self.read_txt_file(file_path)
            
            if not data:
                self.log(f"⚠️ لا توجد بيانات في الملف")
                return 0
            
            self.log(f"📋 قراءة {len(data)} سطر")
            self.log(f"📊 عدد الأعمدة الأصلي: {len(data[0]) if data else 0}")
            
            # حذف الأعمدة x
            cleaned_data = []
            for row in data:
                # التأكد من أن الصف يحتوي على البيانات الكافية
                if len(row) >= len(self.KEEP_COLUMNS):
                    new_row = []
                    for col_index in self.KEEP_COLUMNS:
                        if col_index < len(row):
                            new_row.append(row[col_index])
                        else:
                            new_row.append('')
                    cleaned_data.append(new_row)
                else:
                    # إذا كان الصف أقصر، نحاول التعامل معه
                    new_row = [''] * len(self.FINAL_HEADERS)
                    for i, cell in enumerate(row):
                        if i < len(new_row):
                            new_row[i] = cell
                    cleaned_data.append(new_row)
            
            self.log(f"📊 عدد الأعمدة النهائي: {len(self.FINAL_HEADERS)}")
            
            # حفظ البيانات النظيفة
            if cleaned_data:
                output_file = self.save_cleaned_data(cleaned_data, file_path.stem)
                self.log(f"💾 تم حفظ {len(cleaned_data)} سجل في: {output_file.name}")
                return len(cleaned_data)
            else:
                self.log(f"⚠️ لا توجد بيانات صالحة بعد المعالجة")
                return 0
            
        except Exception as e:
            self.log(f"❌ خطأ في معالجة {file_path.name}: {str(e)}")
            return 0

    def read_csv_file(self, file_path):
        """قراءة ملف CSV"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter='|')
                data = list(reader)[1:]  # تخطي الرؤوس
            return data
        except Exception as e:
            self.log(f"❌ خطأ في قراءة CSV: {str(e)}")
            return []

    def read_txt_file(self, file_path):
        """قراءة ملف TXT"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()[1:]  # تخطي الرؤوس
            
            data = []
            for line in lines:
                line = line.strip()
                if line:
                    parts = line.split(self.PIPE_DELIMITER)
                    data.append(parts)
            return data
        except Exception as e:
            self.log(f"❌ خطأ في قراءة TXT: {str(e)}")
            return []

    def save_cleaned_data(self, data, base_filename):
        """حفظ البيانات النظيفة"""
        output_format = self.output_format.get()
        
        if output_format == "txt":
            # حفظ كملف TXT بالفاصل |
            output_file = Path(self.output_folder.get()) / f"{base_filename}_no_x.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                # كتابة الرؤوس النهائية (بدون x)
                f.write(self.PIPE_DELIMITER.join(self.FINAL_HEADERS) + '\n')
                # كتابة البيانات
                for row in data:
                    f.write(self.PIPE_DELIMITER.join(row) + '\n')
        else:
            # حفظ كملف CSV بالفاصل |
            output_file = Path(self.output_folder.get()) / f"{base_filename}_no_x.csv"
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, delimiter='|')
                writer.writerow(self.FINAL_HEADERS)
                writer.writerows(data)
        
        return output_file


def main():
    """تشغيل أداة حذف الأعمدة x"""
    root = tk.Tk()
    app = XColumnRemover(root)
    root.mainloop()


if __name__ == "__main__":
    main()

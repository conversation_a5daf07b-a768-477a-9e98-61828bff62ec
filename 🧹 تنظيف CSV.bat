@echo off
chcp 65001 >nul
title CSV Cleaner Tool - أداة تنظيف ملفات CSV

echo.
echo ========================================
echo    🧹 CSV Cleaner Tool
echo    أداة تنظيف وتنسيق ملفات CSV
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

REM Check if CSV cleaner file exists
if not exist "csv_cleaner_tool.py" (
    echo ❌ خطأ: ملف csv_cleaner_tool.py غير موجود
    pause
    exit /b 1
)

echo 🚀 جاري تشغيل أداة تنظيف CSV...
echo.

REM Run the CSV cleaner
python csv_cleaner_tool.py

echo.
echo ✅ تم إغلاق أداة تنظيف CSV
pause

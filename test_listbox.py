#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مباشر للـ ListBox
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os

class TestListBox:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧪 اختبار ListBox")
        self.root.geometry("600x400")
        
        self.selected_dbs = []
        
        # إنشاء الواجهة
        self.create_ui()
        
    def create_ui(self):
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill='both', expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="اختبار عرض قواعد البيانات", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار ListBox
        listbox_frame = ttk.LabelFrame(main_frame, text="قواعد البيانات المحددة", padding=10)
        listbox_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # إطار للـ ListBox مع scrollbar
        db_frame = ttk.Frame(listbox_frame)
        db_frame.pack(fill='both', expand=True)
        
        # إنشاء ListBox
        self.db_listbox = tk.Listbox(
            db_frame,
            selectmode='multiple',
            height=10,
            font=('Arial', 11),
            bg='white',
            fg='black',
            selectbackground='#0078d4',
            selectforeground='white'
        )
        
        # scrollbar
        scrollbar = ttk.Scrollbar(db_frame, orient='vertical', command=self.db_listbox.yview)
        self.db_listbox.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.db_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # نص توضيحي
        self.db_listbox.insert(0, "لا توجد قواعد بيانات - انقر 'إضافة' لإضافة ملفات")
        self.db_listbox.config(fg='gray')
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill='x')
        
        ttk.Button(buttons_frame, text="➕ إضافة قاعدة بيانات", 
                  command=self.add_database).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="➖ حذف المحدد", 
                  command=self.remove_selected).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="🗑️ مسح الكل", 
                  command=self.clear_all).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="📊 عرض المعلومات", 
                  command=self.show_info).pack(side='left')
        
        # معلومات الحالة
        self.status_label = ttk.Label(main_frame, text="جاهز للاختبار", 
                                     foreground='blue')
        self.status_label.pack(pady=(10, 0))
        
    def add_database(self):
        """إضافة قواعد بيانات"""
        print("🔍 فتح نافذة اختيار الملفات...")
        
        filetypes = [
            ("جميع الملفات المدعومة", "*.txt;*.csv;*.db"),
            ("ملفات نصية", "*.txt"),
            ("ملفات CSV", "*.csv"),
            ("قواعد بيانات SQLite", "*.db"),
            ("جميع الملفات", "*.*")
        ]
        
        filenames = filedialog.askopenfilenames(
            title="اختر قواعد البيانات",
            filetypes=filetypes
        )
        
        print(f"📁 تم اختيار {len(filenames)} ملف")
        
        if filenames:
            # مسح النص التوضيحي
            if self.db_listbox.size() == 1:
                first_item = self.db_listbox.get(0)
                if "لا توجد قواعد بيانات" in first_item:
                    self.db_listbox.delete(0)
                    self.db_listbox.config(fg='black')
                    print("🧹 تم مسح النص التوضيحي")
            
            added_count = 0
            for filename in filenames:
                if filename and filename not in self.selected_dbs:
                    self.selected_dbs.append(filename)
                    display_name = os.path.basename(filename)
                    self.db_listbox.insert(tk.END, display_name)
                    added_count += 1
                    print(f"✅ تم إضافة: {display_name}")
                    
                    # فرض التحديث
                    self.db_listbox.update()
                    self.root.update_idletasks()
            
            if added_count > 0:
                messagebox.showinfo("نجح", f"تم إضافة {added_count} قاعدة بيانات")
                self.status_label.config(text=f"تم إضافة {added_count} قاعدة بيانات")
                
                # طباعة معلومات التشخيص
                print(f"📊 إجمالي قواعد البيانات: {len(self.selected_dbs)}")
                print(f"📋 عناصر ListBox: {self.db_listbox.size()}")
            else:
                messagebox.showinfo("تنبيه", "جميع الملفات موجودة بالفعل")
        else:
            print("❌ لم يتم اختيار أي ملفات")
    
    def remove_selected(self):
        """حذف العناصر المحددة"""
        selected = self.db_listbox.curselection()
        if not selected:
            messagebox.showwarning("تنبيه", "يرجى اختيار عنصر للحذف")
            return
        
        # حذف من الخلف للأمام
        for index in reversed(selected):
            self.db_listbox.delete(index)
            if index < len(self.selected_dbs):
                del self.selected_dbs[index]
        
        # إضافة النص التوضيحي إذا أصبحت القائمة فارغة
        if self.db_listbox.size() == 0:
            self.db_listbox.insert(0, "لا توجد قواعد بيانات - انقر 'إضافة' لإضافة ملفات")
            self.db_listbox.config(fg='gray')
        
        self.status_label.config(text=f"تم حذف {len(selected)} عنصر")
        print(f"🗑️ تم حذف {len(selected)} عنصر")
    
    def clear_all(self):
        """مسح جميع العناصر"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع قواعد البيانات؟"):
            self.db_listbox.delete(0, tk.END)
            self.selected_dbs.clear()
            
            # إضافة النص التوضيحي
            self.db_listbox.insert(0, "لا توجد قواعد بيانات - انقر 'إضافة' لإضافة ملفات")
            self.db_listbox.config(fg='gray')
            
            self.status_label.config(text="تم مسح جميع قواعد البيانات")
            print("🗑️ تم مسح جميع قواعد البيانات")
    
    def show_info(self):
        """عرض معلومات التشخيص"""
        info = f"""
📊 معلومات التشخيص:

🗂️ عدد قواعد البيانات المحددة: {len(self.selected_dbs)}
📋 عدد عناصر ListBox: {self.db_listbox.size()}

📄 قائمة قواعد البيانات:
"""
        for i, db in enumerate(self.selected_dbs, 1):
            info += f"{i}. {os.path.basename(db)}\n"
        
        info += f"\n📝 محتويات ListBox:\n"
        for i in range(self.db_listbox.size()):
            info += f"{i+1}. {self.db_listbox.get(i)}\n"
        
        messagebox.showinfo("معلومات التشخيص", info)
        print(info)
    
    def run(self):
        """تشغيل الاختبار"""
        print("🚀 بدء اختبار ListBox...")
        self.root.mainloop()

if __name__ == "__main__":
    test = TestListBox()
    test.run()

========================================
    Social Media Data Tools
    Instructions for Use
========================================

PROBLEM SOLVED: Fixed Arabic text issues in batch files!

========================================
    Quick Start
========================================

1. MAIN LAUNCHER:
   Double-click: START_TOOLS.bat
   (This will open both tool folders)

2. CSV CLEANER:
   Go to: CSV_Cleaner_Tool folder
   Double-click: START_CSV_CLEANER.bat

3. DATA SEARCH:
   Go to: Data_Search_Tool folder
   Double-click: START_DATA_SEARCH.bat

========================================
    Available Batch Files
========================================

MAIN FOLDER:
- START_TOOLS.bat                    (RECOMMENDED - Opens both folders)

CSV_CLEANER_TOOL FOLDER:
- START_CSV_CLEANER.bat             (Simple - Just runs the tool)
- run_csv_cleaner.bat               (Detailed - With error checking)

DATA_SEARCH_TOOL FOLDER:
- START_DATA_SEARCH.bat             (Simple - Just runs the tool)
- run_data_search.bat               (Detailed - With error checking)

========================================
    Recommended Workflow
========================================

Step 1: CLEAN YOUR DATA
- Use CSV Cleaner Tool first
- Clean messy CSV files
- Standardize column headers
- Remove duplicates
- Split large files

Step 2: SEARCH IN CLEAN DATA
- Use Data Search Tool
- Search in the cleaned data
- Get faster and more accurate results

========================================
    Troubleshooting
========================================

If you get "command not recognized" errors:
- Use the English-named batch files
- Try: START_CSV_CLEANER.bat instead of Arabic-named files

If Python is not found:
- Install Python 3.6+ from python.org
- Make sure to check "Add Python to PATH"
- Restart your computer after installation

If tools don't start:
- Make sure you're in the correct folder
- Check that .py files exist in the same folder
- Try running: python csv_cleaner.py (manually)

========================================
    File Structure
========================================

Main Folder/
├── START_TOOLS.bat                 (Main launcher)
├── CSV_Cleaner_Tool/
│   ├── csv_cleaner.py
│   ├── START_CSV_CLEANER.bat       (Simple)
│   ├── run_csv_cleaner.bat         (Detailed)
│   └── sample_messy_data.csv
└── Data_Search_Tool/
    ├── data_search.py
    ├── START_DATA_SEARCH.bat       (Simple)
    ├── run_data_search.bat         (Detailed)
    ├── sample_search_ids.txt
    └── sample_database.csv

========================================
    System Requirements
========================================

- Python 3.6+ (Required)
- Windows 7+ / macOS / Linux
- 4GB RAM (8GB+ for large data)
- 1GB free space

========================================
    Success!
========================================

Your tools are now ready to use!
Start with: START_TOOLS.bat

For large databases (13GB+):
1. Use CSV Cleaner first to organize data
2. Then use Data Search for fast searching

This will make your searches 10-50x faster!

========================================
